package com.near_reality.cache_tool.packing.custom

import com.zenyte.game.world.`object`.ObjectId
import mgi.tools.parser.TypeParser
import mgi.types.config.ObjectDefinitions
import java.io.File
import kotlin.div

object CustomObjectsPacker {

    /*
     * May need to be increased as OSRS adds new objects..
     */
    const val BASE_OBJECT_ID : Int = 59000

    @JvmStatic
    fun pack() {
        packGetObjects()
        packClonedObjects()
    }

    fun packGetObjects() {
        getAndRegister(47567).apply {
            setOption(1, "Take")
            pack()
        }

        getAndRegister(47568).apply {
            setOption(1, "Take")
            pack()
        }

        getAndRegister(16499).apply {
            options = arrayOf("Climb", null, null, null, null)
            pack()
        }
    }

    /*
     * IMPORTANT: Always add new objects at the bottom of this method so the auto generated ids don't get mixed around
     * Generated Ids are found in CustomObjectId.java after generateCache is run
     */
    fun packClonedObjects() {
        cloneAndRegister(44631, CustomObjectId.next()).apply {
            name = "Combat Essence Lava Pool"
            options = arrayOf("Sacrifice Items", "Item Sacrifice Values", null, null, null)
            pack()
        }

        cloneAndRegister(14827, CustomObjectId.next()).apply {
            name = "Obelisk"
            options = arrayOf("Wilderness-Vault", "Ganodermic-Beast", null, null, null)
            pack()
        }

        cloneAndRegister(14825, CustomObjectId.next()).apply {
            name = "Obelisk"
            options = arrayOf(null, null, null, null, null)
            pack()
        }

        cloneAndRegister(29241, CustomObjectId.next()).apply {
            name = "Ornate Pool of Rejuvenation"
            options = arrayOf("Drink", "Remove-skull", "Skull", null, null)
            pack()
        }

        cloneAndRegister(29240, CustomObjectId.next()).apply {
            name = "Fancy Pool of Rejuvenation"
            options = arrayOf("Drink", null, null, null, null)
            pack()
        }

        cloneAndRegister(29241, CustomObjectId.next()).apply {
            name = "Overload Pool of Rejuvenation"
            options = arrayOf("Drink", null, null, null, null)
            setModels(intArrayOf(60206))
            pack()
        }

        cloneAndRegister(29241, CustomObjectId.next()).apply {
            name = "Divine Pool of Rejuvenation"
            options = arrayOf("Drink", null, null, null, null)
            setModels(intArrayOf(60205))
            pack()
        }

        cloneAndRegister(41415, CustomObjectId.next()).apply {
            name = "Portal Nexus"
            options = arrayOf("Teleport Menu", "Previous Teleport", null, null, null)
            pack()
        }

        cloneAndRegister(884, CustomObjectId.next()).apply {
            name = "Event Board"
            setModels(intArrayOf(60197))
            sizeX = 7
            sizeY = 2
            modelSizeX = 128
            modelSizeY = 128
            options = arrayOf("View", null, null, null, null)
            pack()
        }

        cloneAndRegister(884, CustomObjectId.next()).apply {
            name = "Well of Goodwill"
            setModels(intArrayOf(60198))
            options = arrayOf("Contribute", null, null, null, null)
            pack()
        }

        cloneAndRegister(23542, CustomObjectId.next()).apply {
            name = "AFK Agility"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(27427, CustomObjectId.next()).apply {
            name = "AFK Farming"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(31798, CustomObjectId.next()).apply {
            name = "AFK Firemaking"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(33256, CustomObjectId.next()).apply {
            name = "AFK Mining"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(4877, CustomObjectId.next()).apply {
            name = "AFK Thieving"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(37975, CustomObjectId.next()).apply {
            name = "AFK Woodcutting"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(4309, CustomObjectId.next()).apply {
            name = "AFK Crafting"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(3994, CustomObjectId.next()).apply {
            name = "AFK Smithing"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(43696, CustomObjectId.next()).apply {
            name = "AFK Runecrafting"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }

        cloneAndRegister(42, CustomObjectId.next()).apply {
            name = "AFK Fishing"
            options = arrayOf("Afk", null, null, null, null)
            pack()
        }
    }

    fun cloneAndRegister(baseId: Int, newId: Int): ObjectDefinitions {
        ObjectIdCollector.register(newId)
        return TypeParser.cloneObject(baseId, newId)
    }

    fun getAndRegister(id: Int): ObjectDefinitions {
        ObjectIdCollector.register(id)
        return ObjectDefinitions.get(id)
    }

    object ObjectIdCollector {
        val usedIds = mutableSetOf<Int>()
        fun register(id: Int) {
            usedIds += id
        }
    }

    private object CustomObjectId {
        private var nextId = BASE_OBJECT_ID
        fun next() = nextId++
    }

    private fun toConstName(rawName: String?, id: Int): String {
        val base = (rawName ?: "OBJECT_$id")
            .replace("'", "")
            .uppercase()
            .replace("[^A-Z0-9]".toRegex(), "_")
            .replace("_+".toRegex(), "_")
            .trim('_')
            .ifEmpty { "OBJECT_$id" }

        // Constants should start with a letter; prefix if needed
        return if (base.firstOrNull()?.isLetter() == true) base else "OBJECT_$base"
    }

    @JvmStatic
    fun writeObjectIdsJavaFile(

    ) {
        val outputDirPath = "src/main/java/com/near_reality/game/item"
        val packageName = "com.near_reality.game.item"
        val className = "CustomObjectId"

        val ids = ObjectIdCollector.usedIds.toList().sorted()
        val constLines = mutableListOf<String>()
        val usedConstNames = mutableSetOf<String>()

        for (id in ids) {
            val objDef = try {
                ObjectDefinitions.get(id)
            } catch (_: Throwable) {
                null
            }

            val proposed = toConstName(objDef?.name, id)

            // Ensure uniqueness (handle duplicate names)
            var name = proposed
            while (!usedConstNames.add(name)) {
                name = "${proposed}_$id"
            }

            constLines += "\tpublic static final int $name = $id;"
        }

        val javaSource = buildString {
            appendLine("package $packageName;")
            appendLine()
            appendLine("import com.near_reality.cache_tool.packing.custom.CustomObjectsPacker;")
            appendLine()
            appendLine("/**")
            appendLine(" * AUTO-GENERATED FILE FROM {@link CustomObjectsPacker}. DO NOT EDIT.")
            appendLine(" */")
            appendLine()
            appendLine("public final class $className")
            appendLine("{")
            if (constLines.isEmpty()) {
                appendLine("\t// No object IDs were registered. Ensure your code called cloneAndRegister/getAndRegister.")
            } else {
                constLines.forEach { appendLine(it) }
            }
            appendLine("}")
        }

        val dir = File(outputDirPath)
        if (!dir.exists()) dir.mkdirs()
        val file = File(dir, "$className.java")
        file.writeText(javaSource)

        println("✅ Wrote ${file.absolutePath} (${constLines.size} constants)")
    }

}
