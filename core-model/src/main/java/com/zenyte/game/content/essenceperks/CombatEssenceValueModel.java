package com.zenyte.game.content.essenceperks;

import java.util.ArrayList;

public class CombatEssenceValueModel {
    private ArrayList<Integer> ids;
    private int value;

    public CombatEssenceValueModel(int value, ArrayList<Integer> itemIds) {
        this.value = value;
        this.ids = itemIds;
    }

    public int getValue() {
        return value;
    }

    public ArrayList<Integer> getIds() {
        return this.ids;
    }

}
