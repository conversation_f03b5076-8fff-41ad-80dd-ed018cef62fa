package com.zenyte.game.content.essenceperks;

public class EssencePerkPriceTable {
    /*
     * Combat Perks
     */
    public static final int v_MeleeISpeed = 1000;
    public static final int v_MeleeIIAccuracy = 2000;
    public static final int v_MeleeIIIDamage = 3000;
    public static final int v_RangedISpeed = 1000;
    public static final int v_RangedIIAccuracy = 2000;
    public static final int v_RangedIIIDamage = 3000;
    public static final int v_MagicISpeed = 1000;
    public static final int v_MagicIIAccuracy = 2000;
    public static final int v_MagicIIIDamage = 3000;
    public static final int v_MeleeLifeLeech = 15000;
    public static final int v_RangedLifeLeech = 20000;
    public static final int v_MagicLifeLeech = 20000;
    public static final int v_CrushWeaponryMaster = 4000;
    public static final int v_StabWeaponryMaster = 4000;
    public static final int v_SlashWeaponryMaster = 4000;
    public static final int v_RuneSaver = 2500;
    public static final int v_AmmoSaver = 2500;
    public static final int v_Berserker = 7500;
    public static final int v_Recoiled = 4000;
    public static final int v_TasteMoreVengeance = 5000;
    public static final int v_SpecialRegen = 5000;
    public static final int v_SoulTiedTwistedBow = 35000;
    public static final int v_SoulTiedScytheOfVitur = 35000;
    public static final int v_SoulTiedTumekensShadow = 35000;
    /*
     * Utility Perks
     */
    public static final int v_UnholyIntervention = 1500;
    public static final int v_BarbarianFisher = 2000;
    public static final int v_ClueSkipper = 2500;
    public static final int v_BoneCruncher = 2000;
    public static final int v_BountifulSacrifice = 3000;
    public static final int v_NoOnesHome = 2500;
    public static final int v_SliceNDice = 8000;
    public static final int v_BarrowsMazeMaster = 2500;
    public static final int v_MasterOfTheCraft = 3000;
    public static final int v_RevItUp = 2500;
    public static final int v_FirstImpressions = 2000;
    public static final int v_ClueCollector = 3000;
    public static final int v_NoPetDebt = 10000;
    public static final int v_CrystalCatalyst = 4000;
    public static final int v_HammerDown = 6000;
    public static final int v_SlayersSpite = 4000;
    public static final int v_SlayerPointBonus = 2500;
    public static final int v_DagannothPeasants = 1500;
    public static final int v_IceForTheEyeless = 1500;
    public static final int v_RunForrestRun = 1500;
    public static final int v_ClueStepMinimizer = 2500;
    public static final int v_HoarderMentality = 2000;
    public static final int v_CorporealScrutiny = 3500;
    public static final int v_CryptKeeper = 2000;
    public static final int v_IWantItAll = 4000;
    public static final int v_DoubleTap = 2500;
    public static final int v_Locksmith = 2500;
    public static final int v_DoublePyro = 1500;
    public static final int v_LethalAttunement = 2000;
    public static final int v_SousChef = 5000;
    public static final int v_Woodsman = 5000;
    public static final int v_DoubleLogs = 5000;
    public static final int v_Pyromaniac = 5000;
    public static final int v_Mixologist = 5000;
    public static final int v_MinerFortyNiner = 5000;
    public static final int v_DoubleOre = 5000;
    public static final int v_Botanist = 5000;
    public static final int v_Fertilizer = 5000;
    public static final int v_TrackStar = 2500;
    public static final int v_SwissArmyMan = 3500;
    public static final int v_Enlightened = 50000;
    public static final int v_Enraged = 50000;
    public static final int v_AnimalTamer = 10000;
    public static final int v_IVoted = 1500;
    public static final int v_BurnBabyBurn = 1500;
    public static final int v_Backfire = 1500;
    public static final int v_ArcaneKnowledge = 1500;
    public static final int v_SleightOfHand = 3000;
    public static final int v_BetterThief = 1000;
    public static final int v_Alchoholic = 1500;
    public static final int v_SustainedAggression = 1500;
    public static final int v_HoleyMoley = 1500;
    public static final int v_TheRedeemer = 3000;
    public static final int v_HolierThanThou = 1500;
    public static final int v_AshesToAshes = 3000;
    public static final int v_BrawnOfJustice = 7500;
    public static final int v_VigourOfInquisition = 25000;
    public static final int v_DoubleChins = 4000;
    public static final int v_InfallibleShackles = 6000;
    public static final int v_IgnoranceIsBliss = 3000;
    public static final int v_ContractKiller = 6000;
    public static final int v_SlayersFavor = 5000;
    public static final int v_FarmersFortune = 1500;
    public static final int v_SoulStealer = 7500;
    public static final int v_SlayersSovereignty = 10000;
    public static final int v_HolyInterventionI = 5000;
    public static final int v_HolyInterventionII = 10000;
    public static final int v_HolyInterventionIII = 20000;
    public static final int v_MinionsMight = 15000;
    public static final int v_FamiliarsFortune = 10000;
    public static final int v_TheLegendaryFisherman = 2500;
    public static final int v_DoubleFish = 5000;
    public static final int v_DullAxes = 3500;
    public static final int v_EyeDontSeeYou = 5000;
    public static final int v_AllGassedUp = 5000;
    public static final int v_JabbasRightHand = 12500;
}
