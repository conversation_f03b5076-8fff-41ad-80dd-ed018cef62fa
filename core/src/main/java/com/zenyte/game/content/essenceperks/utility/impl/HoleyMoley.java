package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class HoleyMoley extends <PERSON><PERSON>cePerk {
    @Override
    public String name() {
        return "<PERSON><PERSON>";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_HoleyMoley;
    }

    @Override
    public String description() {
        return "Mole skins and mole claws are now doubled upon dropping from the Giant Mole";
    }

    @Override
    public int item() {
        return ItemId.MOLE_CLAW;
    }
}
