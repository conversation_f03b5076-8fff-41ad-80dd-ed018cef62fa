package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.DisabledEssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

@DisabledEssencePerk
public class HolierThanThou extends EssencePerk {
    @Override
    public String name() {
        return "Holier Than Thou";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_HolierThanThou;
    }

    @Override
    public String description() {
        return "Provides a passive 15% damage boost to the Angel of Death";
    }

    @Override
    public int item() {
        return ItemId.ANGELIC_ARTIFACT;
    }
}
