package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

public class IVoted extends <PERSON><PERSON>cePerk {

    public static boolean roll() {
        return Utils.random(9) <= 2;
    }

    @Override
    public String name() {
        return "I Voted";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_IVoted;
    }

    @Override
    public String description() {
        return "Provides a 30% chance to double vote rewards when claiming";
    }

    @Override
    public int item() {
        return 2996;
    }
}
