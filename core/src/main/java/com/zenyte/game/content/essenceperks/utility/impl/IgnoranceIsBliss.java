package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class IgnoranceIsBliss extends EssencePerk {
    @Override
    public String name() {
        return "Ignorance Is Bliss";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_IgnoranceIsBliss;
    }

    @Override
    public String description() {
        return "Provides a passive effect to remove <PERSON> Muspah's prayer shield";
    }

    @Override
    public int item() {
        return ItemId.MUPHIN;
    }
}
