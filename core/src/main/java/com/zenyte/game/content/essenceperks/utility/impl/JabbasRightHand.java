package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.DisabledEssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

@DisabledEssencePerk
public class JabbasRightHand extends <PERSON>ssencePerk {
    @Override
    public String name() {
        return "Jab<PERSON>'s Right Hand";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_JabbasRightHand;
    }

    @Override
    public String description() {
        return "Bounty Hunter weapons and armor exclusive boosts now apply in PvM as well as adding a 10% damage boost to weapons against PvM targets.";
    }

    @Override
    public int item() {
        return ItemId.ESOTERIC_EMBLEM_TIER_10;
    }
}
