package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class LethalAttunement extends EssencePerk {
    @Override
    public String name() {
        return "Lethal Attunement";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_LethalAttunement;
    }

    @Override
    public String description() {
        return "Provides the ability that revenant weapons no longer require ether charges to use.";
    }

    @Override
    public int item() {
        return 22547;
    }
}
