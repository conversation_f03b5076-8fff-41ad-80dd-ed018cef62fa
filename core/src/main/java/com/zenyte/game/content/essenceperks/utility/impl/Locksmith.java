package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.util.Utils;

public class <PERSON><PERSON> extends <PERSON>ssencePerk {
    public static boolean roll() {
        // 30%
        return Utils.random(9) <= 2;
    }
    @Override
    public String name() {
        return "<PERSON><PERSON>";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_Locksmith;
    }

    @Override
    public String description() {
        return "Provides a 30% chance to save a crystal key when opening the crystal chest.";
    }

    @Override
    public int item() {
        return 989;
    }
}
