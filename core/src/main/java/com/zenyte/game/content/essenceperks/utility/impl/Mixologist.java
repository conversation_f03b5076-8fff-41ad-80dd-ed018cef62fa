package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

import java.util.concurrent.ThreadLocalRandom;

public class Mixologist extends EssencePerk {

    public static boolean roll() {
        return ThreadLocalRandom.current().nextDouble() <= 0.2;
    }

    @Override
    public String name() {
        return "Mixologist";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_Mixologist;
    }

    @Override
    public String description() {
        return "Provides a 20% chance to make two potions instead of one.";
    }

    @Override
    public int item() {
        return 2434;
    }
}
