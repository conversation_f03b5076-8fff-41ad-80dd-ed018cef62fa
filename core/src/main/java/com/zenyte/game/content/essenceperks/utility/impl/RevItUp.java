package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class RevItUp extends EssencePerk {
    @Override
    public String name() {
        return "Rev' it up";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_RevItUp;
    }

    @Override
    public String description() {
        return "Provides a 2x boost to coins and ether dropped by revenants.";
    }

    @Override
    public int item() {
        return 21820;
    }
}
