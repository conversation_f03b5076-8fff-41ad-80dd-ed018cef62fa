package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class RunForrestRun extends EssencePerk {

    @Override
    public String name() {
        return "Run Forrest, Run!";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_RunForrestRun;
    }

    @Override
    public String description() {
        return "Provides infinite run energy. (excluding Wilderness Areas)";
    }

    @Override
    public int item() {
        return 11860;
    }
}
