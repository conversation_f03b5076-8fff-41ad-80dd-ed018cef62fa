package com.zenyte.game.content.essenceperks.utility.impl;

import com.near_reality.game.item.CustomItemId;
import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class SlayerPointBonus extends EssencePerk {

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SlayerPointBonus;
    }

    @Override
    public String name() {
        return "Slayer Point Bonus";
    }

    @Override
    public String description() {
        return "Get an additional 15% slayer points for finishing slayer tasks.";
    }

    @Override
    public int item() {
        return CustomItemId.SLAYER_BOOSTER;
    }
}
