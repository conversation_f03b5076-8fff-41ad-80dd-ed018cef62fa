package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class SlayersFavor extends <PERSON><PERSON>cePerk {
    @Override
    public String name() {
        return "Slayer's Favor";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SlayersFavor;
    }

    @Override
    public String description() {
        return "Provides a passive boost that raises superior slayer monster spawn rate by 25%";
    }

    @Override
    public int item() {
        return ItemId.MYSTIC_AIR_STAFF;
    }
}
