package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class SlayersSovereignty extends EssencePerk {
    @Override
    public String name() {
        return "Slayer's Sovereignty";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SlayersSovereignty;
    }

    @Override
    public String description() {
        return "Slayer helm and statue passive effects now work off task.";
    }

    @Override
    public int item() {
        return ItemId.BLACK_SLAYER_HELMET;
    }
}
