package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class SlayersSpite extends EssencePerk {
    @Override
    public String name() {
        return "Slayer's Spite";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SlayersSpite;
    }

    @Override
    public String description() {
        return "Provides a 5% damage and accuracy boost when wearing a Slayer Helmet.";
    }

    @Override
    public int item() {
        return 11864;
    }
}
