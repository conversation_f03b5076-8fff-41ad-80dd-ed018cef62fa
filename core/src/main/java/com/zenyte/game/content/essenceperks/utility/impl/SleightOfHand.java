package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class SleightOfHand extends EssencePerk {
    @Override
    public String name() {
        return "Sleight of Hand";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SleightOfHand;
    }

    @Override
    public String description() {
        return "Provides the ability to automatically re-pickpocket an npc until you can no longer do so.";
    }

    @Override
    public int item() {
        return 22521;
    }
}
