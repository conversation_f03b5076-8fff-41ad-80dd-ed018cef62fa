package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class SousChef extends EssencePerk {
    @Override
    public String name() {
        return "Sous Chef";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_SousChef;
    }

    @Override
    public String description() {
        return "Provides a 100% increase to cooking speed and prevents burning.";
    }

    @Override
    public int item() {
        return 7441;
    }
}
