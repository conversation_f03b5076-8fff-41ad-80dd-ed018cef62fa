package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class TheLegendaryFisherman extends <PERSON>ssencePerk {
    @Override
    public String name() {
        return "The Legendary Fisherman";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_TheLegendaryFisherman;
    }

    @Override
    public String description() {
        return "While fishing, you have the ability to catch a fish twice as often.";
    }

    @Override
    public int item() {
        return ItemId.HARPOON;
    }
}
