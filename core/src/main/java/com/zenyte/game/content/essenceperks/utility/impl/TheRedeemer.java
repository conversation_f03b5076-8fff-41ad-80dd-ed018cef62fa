package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;
import com.zenyte.game.item.ItemId;

public class TheRedeemer extends <PERSON><PERSON>cePerk {
    @Override
    public String name() {
        return "The Redeemer";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_TheRedeemer;
    }

    @Override
    public String description() {
        return "The Redemption prayer now heals you to 50 hp or your max hp (whichever is lower)";
    }

    @Override
    public int item() {
        return 3840;
    }
}
