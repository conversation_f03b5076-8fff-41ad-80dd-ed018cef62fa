package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class TrackStar extends EssencePerk {
    @Override
    public String name() {
        return "Track Star";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_TrackStar;
    }

    @Override
    public String description() {
        return "Provides passive Agility xp gains whilst running.";
    }

    @Override
    public int item() {
        return 11849;
    }
}
