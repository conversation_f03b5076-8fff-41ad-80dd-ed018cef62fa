package com.zenyte.game.content.essenceperks.utility.impl;

import com.zenyte.game.content.essenceperks.EssencePerk;
import com.zenyte.game.content.essenceperks.EssencePerkPriceTable;

public class <PERSON><PERSON> extends <PERSON>ssencePerk {
    @Override
    public String name() {
        return "<PERSON><PERSON>";
    }

    @Override
    public int price() {
        return EssencePerkPriceTable.v_Woodsman;
    }

    @Override
    public String description() {
        return "Provides a 100% increase to woodcutting speed and raises bird nest rates.";
    }

    @Override
    public int item() {
        return 6739;
    }
}
