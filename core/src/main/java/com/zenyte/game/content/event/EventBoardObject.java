package com.zenyte.game.content.event;

import com.near_reality.game.item.CustomItemId;
import com.near_reality.game.item.CustomObjectId;
import com.zenyte.game.content.worldevent.donatorboss.XamphurHandler;
import com.zenyte.game.content.worldevent.voteboss.GalvekHandler;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.plugins.dialogue.ItemChat;
import com.zenyte.plugins.dialogue.OptionsMenuD;

/**
 * <AUTHOR> (Discord: astra4)
 */
@SuppressWarnings("unused")
public class EventBoardObject implements ObjectAction {

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        player.getDialogueManager().start(new OptionsMenuD(player, "Please Select an Event.", EventType.names) {
            @Override
            public void handleClick(int slotId) {
                switch (slotId) {
                    case 0: //Vote Boss
                        if(!GalvekHandler.get().isGalvekActive()) {
                            player.getDialogueManager().start(new ItemChat(player, CustomItemId.NR_VOTE_SHARD, "The Vote Boss is not active at this time."));
                            break;
                        }
                        player.sendMessage("You teleport to the Vote Boss.");
                        player.teleport(EventType.values[slotId].getLocation());
                        break;

                    case 1: //Donator Boss
                        if(!XamphurHandler.get().isXamphurActive()) {
                            player.getDialogueManager().start(new ItemChat(player, ItemId.DONATOR_BOND_10, "The Donation Boss is not active at this time."));
                            break;
                        }

                        player.sendMessage("You teleport to the Vote Boss.");
                        player.teleport(EventType.values[slotId].getLocation());
                        break;

                    case 2: //Holiday Event
                        player.getDialogueManager().start(new ItemChat(player, ItemId.EASTER_BASKET, "There is currently no active Holiday Event."));
                        break;
                }
            }
        });
    }

    @Override
    public Object[] getObjects() {
        return new Object[] {CustomObjectId.EVENT_BOARD};
    }

}
