package com.zenyte.game.content.event;

import com.zenyte.game.world.entity.Location;

/**
 * <AUTHOR> (Discord: astra4)
 */
public enum EventType {
    VOTE_BOSS("Vote Boss", new Location(1631, 5718, 2)),
    DONATOR_BOSS("Donator Boss", new Location(3360, 7063, 0)),
    HOLIDAY("Current Holiday Event", new Location(3081, 3482, 0))
    ;

    public static final EventType[] values = values();
    public static final String[] names = new String[values.length];
    private final String name;
    private final Location location;

    EventType(String name, Location location) {
        this.name = name;
        this.location = location;
    }

    public Location getLocation() {
        return location;
    }

    static {
        for (int i = 0, length = values.length; i < length; i++) {
            names[i] = values[i].name;
        }
    }

}
