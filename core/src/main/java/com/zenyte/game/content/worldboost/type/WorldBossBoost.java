package com.zenyte.game.content.worldboost.type;

import com.zenyte.game.content.worldboost.WorldBoostType;
import com.zenyte.game.world.broadcasts.BroadcastType;

public enum WorldBossBoost implements WorldBoostType {

//    EXP_BOOST_50("XP Boost (%s)"),
//    MARKS_OF_GRACE_X2("Marks of Grace (x2)"),
//    VOTE_LOYALTY_X2("Vote & Loyalty points (x2)"),
//    COIN_BG_RESOURCE_CLUES_CKEYS_X2("x2 Coin bags, Resource Boxes, Clue Scroll, and Crystal keys"),
//    FASTER_FISHING_50PCNT("Fishing Speed (50% faster)"),
//    WINTERTODT_50PCNT_BOOST("Bonus Wintertodt Points (50% point increase)"),
//    BONUS_SEEDS_MSTR_FARMER_50PCNT("Master farmer (bonus)"),
    
    GOLDEN_NUGGETS_X2("Golden Nuggets (x2)"),
    SLAYER_POINTS_X2("Slayer Points (x2)"),
    RC_RUNES_X2("Runecrafting (x2 Runes)"),
    BRIMSTONE_KEY_DROPS_X2("Brimstone Keys (x2)"),
    LARRANS_KEY_DROPS_X2("Larrans Chest (x2 Loot)"),
    BONUS_CLUE_LOOT("Clue Rewards Bonus"),
    BONUS_BLOOD_MONEY("Blood Money (x2)"),
    BONUS_ZALCANO_LOOT("Zalcano (x2 Loot)"),
    BONUS_BARROWS_DR_25("Barrows (25% Drop rate)"),
    BONUS_SLAYER_SUPERIOR("Superior Slayer (x2)"),
    BONUX_COX_POINTS_25PCNT("CoX points (25% more)"),
    BONUS_PET_RATES("Bonus Pet Rates (25%)"),
    BONUS_GAUNTLET("Gauntlet (x2 Loot)"),
    DOUBLE_COINS("Coins (x2 amount)"),
    PEST_CONTROL("Pest control points (50%)"),
    NIGHTMARE("Nightmare (25% Drop rate)"),
    TOB_PURPLE_BOOST("ToB Uniques (20% more)"),
    TOA_BOOST("ToA Uniques (20% more)"),
    NEX_BOOST("Nex Uniques (20% more)"),
    HERB_SPEED_BOOST("Herb Grow Times (x2)")
    ;

    public static final WorldBossBoost[] VALUES = values();
    private final String message;

    WorldBossBoost(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public BroadcastType getBroadcastType() {
        return BroadcastType.DONATOR_BOSS;
    }

}
