package com.zenyte.game.content.worldevent.highlightedskill;

import com.zenyte.game.content.worldboost.WorldBoost;
import com.zenyte.game.content.worldboost.type.WorldSkillBoost;
import com.zenyte.game.task.TickTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.broadcasts.WorldBroadcasts;
import com.zenyte.utils.TimeUnit;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;

import static com.zenyte.game.world.broadcasts.BroadcastType.LOTTERY;

/**
 * <AUTHOR> (Discord: astra4)
 */
public final class ScheduledHighlightedSkill extends TickTask {

    private final WorldSkillBoost highlightedSkill;

    public ScheduledHighlightedSkill(int ticks, WorldSkillBoost highlightedSkill) {
        this.ticks = ticks;
        this.highlightedSkill = highlightedSkill;
    }

    @Override
    public void run() {
        if (ticks == 0) {

            List<WorldSkillBoost> availableBoosts = new ArrayList<>(List.of(WorldSkillBoost.VALUES));
            WorldSkillBoost skillBoost = Utils.random(availableBoosts);
            if (skillBoost == null) {
                return;
            }

            long endTime = System.currentTimeMillis() + TimeUnit.HOURS.toMillis(2);
            WorldBoost worldBoost = new WorldBoost(skillBoost, endTime, 2);
            worldBoost.activate(true);

            // Schedule the next highlighted skill
            int ticks = (int) TimeUnit.HOURS.toTicks(2) + (int) TimeUnit.MINUTES.toTicks(30);
            ScheduledHighlightedSkill scheduler = selectRandomSkillExcept(highlightedSkill, ticks);
            WorldTasksManager.schedule(scheduler, 0, 0);

            stop();
            return;
        }

        if (ticks == TimeUnit.MINUTES.toTicks(5)) {
            WorldBroadcasts.sendMessage("<col=00FF00><shad=000000>A new highlighted skill will be selected in 5 minutes!", LOTTERY, true);
        }

        ticks--;
    }

    public static ScheduledHighlightedSkill selectRandomSkillExcept(WorldSkillBoost except, int ticks) {
        Set<WorldSkillBoost> skills = EnumSet.copyOf(WorldSkillBoost.SKILLS);
        if (except != null) {
            skills.remove(except);
        }

        WorldSkillBoost skill = Utils.getRandomCollectionElement(skills);
        if (skill == null) {
            throw new IllegalStateException("Unable to select skill; null when trying to generate random skill");
        }

        return new ScheduledHighlightedSkill(ticks, skill);
    }

    public static ScheduledHighlightedSkill selectRandomSkill(int ticks) {
        return selectRandomSkillExcept(null, ticks);
    }

    public long getTicks() {
        return ticks;
    }
}
