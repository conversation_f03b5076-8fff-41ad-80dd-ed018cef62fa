package com.zenyte.game.content.worldevent.voteboss;


import com.zenyte.game.world.Position;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.PolygonRegionArea;
import com.zenyte.game.world.region.RSPolygon;
import com.zenyte.game.world.region.area.plugins.CannonRestrictionPlugin;
import kotlin.Pair;

/**
 * <AUTHOR> (Discord: astra4)
 */
public final class GalvekArea extends PolygonRegionArea implements CannonRestrictionPlugin {

    @Override
    public RSPolygon[] polygons() {
        return new RSPolygon[]{new RSPolygon(6489)};
    }

    @Override
    public void enter(Player player) {
    }

    @Override
    public void leave(Player player, boolean logout) {
        if(player.getHpHud() != null)
            player.getHpHud().close();

        final Galvek galvek = GalvekHandler.get().getGalvek();
        if (galvek != null) {
            player.sendDeveloperMessage("Galvek -> clearing your damage contributions to Galvek.");
            galvek.getReceivedDamage().remove(new Pair<>(player.getUsername().toLowerCase(), player.getGameMode()));
        }
    }

    @Override
    public boolean isMultiwayArea(Position position) {
        return true;
    }

    @Override
    public boolean safeHardcoreIronmanDeath() {
        return true;
    }

    @Override
    public String name() {
        return "Vote Boss";
    }
}
