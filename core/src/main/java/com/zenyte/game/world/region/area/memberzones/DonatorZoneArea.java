package com.zenyte.game.world.region.area.memberzones;

import com.google.common.eventbus.Subscribe;
import com.near_reality.game.item.CustomObjectId;
import com.zenyte.game.world.World;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.privilege.MemberRank;
import com.zenyte.game.world.object.WorldObject;
import com.zenyte.game.world.region.PolygonRegionArea;
import com.zenyte.game.world.region.RSPolygon;
import com.zenyte.plugins.events.ServerLaunchEvent;
import com.zenyte.plugins.renewednpc.ExilesGuide;


/**
 * <AUTHOR> (Discord: imslickk)
 */
public class DonatorZoneArea extends PolygonRegionArea {

    @Subscribe
    public static void onServerLaunched(ServerLaunchEvent event) {
        World.spawnObject(new WorldObject(14888, 10, 2, new Location( 3365, 7206, 1))); //pottery oven
        World.spawnObject(new WorldObject(50067, 10, 0, new Location( 3371, 7204, 1))); //DI_TREE Patch
        World.spawnObject(new WorldObject(50069, 10, 0, new Location( 3367, 7205, 1))); //DI_HERB Patch
        World.spawnObject(new WorldObject(CustomObjectId.WELL_OF_GOODWILL, 10, 0, new Location( 3360, 7198, 1))); //DI_WELL_OF_GOODWILL
        World.spawnObject(new WorldObject(CustomObjectId.ORNATE_POOL_OF_REJUVENATION, 10, 0, new Location( 3365, 7196, 1))); //DI_FANCY_POOL_OF_REJUVENATION
    }

    @Override
    public RSPolygon[] polygons() {
        return new RSPolygon[]{new RSPolygon(13424),
                new RSPolygon(11374), new RSPolygon(11375), new RSPolygon(11376), new RSPolygon(11377), new RSPolygon(11378), new RSPolygon(11379)}; //Barrows Areas
    }

    @Override
    public void enter(Player player) {
        boolean isDonator = player.getMemberRank().equalToOrGreaterThan(MemberRank.REGULAR);
        if (!isDonator && !player.isStaff()) {
            player.sendMessage("You must be a donator to enter this area.");
            player.teleport(new Location(ExilesGuide.SPAWN_LOCATION.getX(), ExilesGuide.SPAWN_LOCATION.getY(), 0)); // force home
        }
    }

    @Override
    public void leave(Player player, boolean logout) {
    }

    @Override
    public String name() {
        return "Donator Zone";
    }

}

