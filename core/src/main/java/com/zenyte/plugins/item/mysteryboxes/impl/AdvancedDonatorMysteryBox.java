package com.zenyte.plugins.item.mysteryboxes.impl;

import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.item.pluginextensions.ItemPlugin;
import com.zenyte.game.world.entity.npc.drop.viewerentry.DropViewerEntry;
import com.zenyte.game.world.entity.npc.drop.viewerentry.OtherDropViewerEntry;
import com.zenyte.plugins.interfaces.MysteryBoxInterface;
import com.zenyte.plugins.item.mysteryboxes.MysteryItem;
import com.zenyte.plugins.item.mysteryboxes.MysterySupplyItem;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class AdvancedDonatorMysteryBox extends ItemPlugin {

    public static int totalWeight;
    public static MysteryItem[] rewards;
    public static MysterySupplyItem[] supplies;

    public static ObjectArrayList<DropViewerEntry> entries = new ObjectArrayList<>();
    public static ObjectArrayList<DropViewerEntry> toEntries() {
        if(entries.size() == 0) {
            calculateEntries();
        }
        return entries;
    }

    private static void calculateEntries() {
        for (final MysteryItem reward : rewards) {
            OtherDropViewerEntry entry = new OtherDropViewerEntry(reward.getId(), reward.getMinAmount(), reward.getMaxAmount(), reward.getWeight(), totalWeight, "");
            entries.add(entry);
        }
    }

    @Override
    public void handle() {
        bind("Open", (player, item, container, slotId) -> MysteryBoxInterface.openBoxQuick(player, item.getId(), rewards, totalWeight));

        rewards = new MysteryItem[] {
                //Common
                new MysteryItem(ItemId.ZAMORAK_GODSWORD, 1, 1, 1000),
                new MysteryItem(ItemId.BERSERKER_RING_I, 1, 1, 1000),
                new MysteryItem(ItemId.ARCHERS_RING_I, 1, 1, 1000),
                new MysteryItem(ItemId.WARRIOR_RING_I, 1, 1, 1000),
                new MysteryItem(ItemId.SEERS_RING_I, 1, 1, 1000),
                new MysteryItem(ItemId.BRIMSTONE_RING, 1, 1, 1000),
                new MysteryItem(ItemId.FIGHTER_TORSO, 1, 1, 1000).setCheckHasItem(),
                new MysteryItem(ItemId.FIRE_CAPE, 1, 1, 1000).setCheckHasItem(),
                new MysteryItem(ItemId.ABYSSAL_WHIP, 1, 1, 1000),
                new MysteryItem(ItemId.SARADOMIN_SWORD, 1, 1, 1000),
                new MysteryItem(ItemId.AMULET_OF_FURY, 1, 1, 1000),
                new MysteryItem(ItemId.BERSERKER_NECKLACE, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_BOOTS, 1, 1, 1000),
                new MysteryItem(ItemId.BANDOS_BOOTS, 1, 1, 1000),
                new MysteryItem(ItemId.OCCULT_NECKLACE, 1, 1, 1000),
                new MysteryItem(ItemId.TYRANNICAL_RING_I, 1, 1, 1000),
                new MysteryItem(ItemId.RING_OF_THE_GODS_I, 1, 1, 1000),
                new MysteryItem(ItemId.TREASONOUS_RING_I, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_CROSSBOW, 1, 1, 1000),
                new MysteryItem(ItemId.TRIDENT_OF_THE_SEAS_FULL, 1, 1, 1000),
                new MysteryItem(ItemId.TOME_OF_FIRE_EMPTY, 1, 1, 1000),
                new MysteryItem(ItemId.INFINITY_HAT, 1, 1, 1000),
                new MysteryItem(ItemId.INFINITY_TOP, 1, 1, 1000),
                new MysteryItem(ItemId.INFINITY_BOTTOMS, 1, 1, 1000),
                new MysteryItem(ItemId.INFINITY_GLOVES, 1, 1, 1000),
                new MysteryItem(ItemId.INFINITY_BOOTS, 1, 1, 1000),
                new MysteryItem(ItemId.MAGES_BOOK, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_AXE, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_PICKAXE, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_HARPOON, 1, 1, 1000),
                new MysteryItem(ItemId.DWARF_CANNON_SET, 1, 1, 1000),
                new MysteryItem(ItemId.RANGERS_TUNIC, 1, 1, 1000),
                new MysteryItem(ItemId.ODIUM_WARD, 1, 1, 1000),
                new MysteryItem(ItemId.MALEDICTION_WARD, 1, 1, 1000),
                new MysteryItem(ItemId.LIGHT_BALLISTA, 1, 1, 1000),
                new MysteryItem(ItemId.SARACHNIS_CUDGEL, 1, 1, 1000),
                new MysteryItem(ItemId.BOTTOMLESS_COMPOST_BUCKET, 1, 1, 1000),
                new MysteryItem(ItemId.THAMMARONS_SCEPTRE_U, 1, 1, 1000),
                new MysteryItem(ItemId.ANGLERFISH + 1, 2500, 5000, 1000),
                new MysteryItem(ItemId.SUPER_COMBAT_POTION4 + 1, 500, 1000, 1000),

                //Uncommon
                new MysteryItem(ItemId.ARMADYL_GODSWORD, 1, 1, 500),
                new MysteryItem(ItemId.BLOOD_SHARD, 1, 1, 500),
                new MysteryItem(ItemId.BANDOS_TASSETS, 1, 1, 500),
                new MysteryItem(ItemId.BANDOS_CHESTPLATE, 1, 1, 500),
                new MysteryItem(ItemId.ARMADYL_CHAINSKIRT, 1, 1, 500),
                new MysteryItem(ItemId.ARMADYL_CHESTPLATE, 1, 1, 500),
                new MysteryItem(ItemId.ARMADYL_HELMET, 1, 1, 500),
                new MysteryItem(ItemId.STAFF_OF_THE_DEAD, 1, 1, 500),
                new MysteryItem(ItemId.ARMADYL_CROSSBOW, 1, 1, 500),
                new MysteryItem(ItemId.ZAMORAKIAN_SPEAR, 1, 1, 500),
                new MysteryItem(ItemId.TANZANITE_FANG, 1, 1, 500),
                new MysteryItem(ItemId.MAGIC_FANG, 1, 1, 500),
                new MysteryItem(ItemId.SERPENTINE_VISAGE, 1, 1, 500),
                new MysteryItem(ItemId.GUTHANS_ARMOUR_SET, 1, 1, 500),
                new MysteryItem(ItemId.VERACS_ARMOUR_SET, 1, 1, 500),
                new MysteryItem(ItemId.DHAROKS_ARMOUR_SET, 1, 1, 500),
                new MysteryItem(ItemId.TORAGS_ARMOUR_SET, 1, 1, 500),
                new MysteryItem(ItemId.AHRIMS_ARMOUR_SET, 1, 1, 500),
                new MysteryItem(ItemId.KARILS_ARMOUR_SET, 1, 1, 500),
                new MysteryItem(ItemId.AMULET_OF_THE_DAMNED_FULL, 1, 1, 500),
                new MysteryItem(ItemId.KRAKEN_TENTACLE, 1, 1, 500),
                new MysteryItem(ItemId.DRAGONFIRE_SHIELD_11284, 1, 1, 500),
                new MysteryItem(ItemId.ARCANE_PRAYER_SCROLL, 1, 1, 500),
                new MysteryItem(ItemId.DEXTEROUS_PRAYER_SCROLL, 1, 1, 500),
                new MysteryItem(ItemId.DINHS_BULWARK, 1, 1, 500),
                new MysteryItem(ItemId.FEROCIOUS_GLOVES, 1, 1, 500),
                new MysteryItem(ItemId.ABYSSAL_DAGGER, 1, 1, 500),
                new MysteryItem(ItemId.ABYSSAL_BLUDGEON, 1, 1, 500),
                new MysteryItem(ItemId.DRAGONFIRE_WARD, 1, 1, 500),
                new MysteryItem(ItemId.ANCIENT_WYVERN_SHIELD, 1, 1, 500),
                new MysteryItem(ItemId.DEVOUT_BOOTS, 1, 1, 500),
                new MysteryItem(ItemId.HEAVY_BALLISTA, 1, 1, 500),
                new MysteryItem(ItemId.CRAWS_BOW_U, 1, 1, 500),
                new MysteryItem(ItemId.VIGGORAS_CHAINMACE_U, 1, 1, 500),

                //Rare
                new MysteryItem(ItemId.SARADOMIN_GODSWORD, 1, 1, 250),
                new MysteryItem(ItemId.BANDOS_GODSWORD, 1, 1, 250),
                new MysteryItem(ItemId.ARCANE_SIGIL, 1, 1, 250),
                new MysteryItem(ItemId.SPECTRAL_SIGIL, 1, 1, 250),
                new MysteryItem(ItemId.ANCESTRAL_HAT, 1, 1, 250),
                new MysteryItem(ItemId.ANCESTRAL_ROBE_TOP, 1, 1, 250),
                new MysteryItem(ItemId.ANCESTRAL_ROBE_BOTTOM, 1, 1, 250),
                new MysteryItem(ItemId.DRAGON_HUNTER_CROSSBOW, 1, 1, 250),
                new MysteryItem(ItemId.DRAGON_HUNTER_LANCE, 1, 1, 250),
                new MysteryItem(ItemId.AMULET_OF_TORTURE, 1, 1, 250),
                new MysteryItem(ItemId.NECKLACE_OF_ANGUISH, 1, 1, 250),
                new MysteryItem(ItemId.TORMENTED_BRACELET, 1, 1, 250),
                new MysteryItem(ItemId.RING_OF_SUFFERING, 1, 1, 250),
                new MysteryItem(ItemId.DRAGON_WARHAMMER, 1, 1, 250),
                new MysteryItem(ItemId.IMBUED_HEART, 1, 1, 250),
                new MysteryItem(ItemId.NIGHTMARE_STAFF, 1, 1, 250),
                new MysteryItem(ItemId.VOLATILE_ORB, 1, 1, 250),
                new MysteryItem(ItemId.ELDRITCH_ORB, 1, 1, 250),
                new MysteryItem(ItemId.INQUISITORS_GREAT_HELM, 1, 1, 250),
                new MysteryItem(ItemId.INQUISITORS_HAUBERK, 1, 1, 250),
                new MysteryItem(ItemId.INQUISITORS_PLATESKIRT, 1, 1, 250),
                new MysteryItem(ItemId.PRIMORDIAL_BOOTS, 1, 1, 250),
                new MysteryItem(ItemId.PEGASIAN_BOOTS, 1, 1, 250),
                new MysteryItem(ItemId.ETERNAL_BOOTS, 1, 1, 250),
                new MysteryItem(ItemId.ELDER_MAUL, 1, 1, 250),
                new MysteryItem(ItemId.INFERNAL_CAPE, 1, 1, 250),
                new MysteryItem(ItemId.JUSTICIAR_FACEGUARD, 1, 1, 250),
                new MysteryItem(ItemId.JUSTICIAR_CHESTGUARD, 1, 1, 250),
                new MysteryItem(ItemId.JUSTICIAR_LEGGUARDS, 1, 1, 250),
                new MysteryItem(ItemId.GHRAZI_RAPIER, 1, 1, 250),
                new MysteryItem(ItemId.SANGUINESTI_STAFF_UNCHARGED, 1, 1, 250),
                new MysteryItem(ItemId.AVERNIC_DEFENDER_HILT, 1, 1, 250),
                new MysteryItem(ItemId.VESTAS_CHAINBODY, 1, 1, 250),
                new MysteryItem(ItemId.VESTAS_PLATESKIRT, 1, 1, 250),
                new MysteryItem(ItemId.VESTAS_LONGSWORD, 1, 1, 250),
                new MysteryItem(ItemId.VESTAS_SPEAR, 1, 1, 250),
                new MysteryItem(ItemId.STATIUSS_FULL_HELM, 1, 1, 250),
                new MysteryItem(ItemId.STATIUSS_PLATEBODY, 1, 1, 250),
                new MysteryItem(ItemId.STATIUSS_PLATELEGS, 1, 1, 250),
                new MysteryItem(ItemId.ZURIELS_HOOD, 1, 1, 250),
                new MysteryItem(ItemId.ZURIELS_ROBE_TOP, 1, 1, 250),
                new MysteryItem(ItemId.ZURIELS_ROBE_BOTTOM, 1, 1, 250),
                new MysteryItem(ItemId.ZURIELS_STAFF, 1, 1, 250),
                new MysteryItem(ItemId.MORRIGANS_COIF, 1, 1, 250),
                new MysteryItem(ItemId.MORRIGANS_LEATHER_BODY, 1, 1, 250),
                new MysteryItem(ItemId.MORRIGANS_LEATHER_CHAPS, 1, 1, 250),
                new MysteryItem(ItemId.BASILISK_JAW, 1, 1, 250),
                new MysteryItem(ItemId.RANGER_BOOTS, 1, 1, 250),

                //Really Rare
                new MysteryItem(ItemId.HARMONISED_ORB, 1, 1, 100).announce(),
                new MysteryItem(ItemId.INQUISITORS_MACE, 1, 1, 100).announce(),
                new MysteryItem(ItemId.KODAI_WAND, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_WAND, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_LONGSWORD, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_BOW, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_AXE, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_PICKAXE, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_AMULET, 1, 1, 100).announce(),
                new MysteryItem(ItemId._3RD_AGE_CLOAK, 1, 1, 100).announce(),

                //Extremely Rare
                new MysteryItem(ItemId.ELYSIAN_SIGIL, 1, 1, 50).announce(),

                //Uber Rare
                new MysteryItem(ItemId.OSMUMTENS_FANG, 1, 1, 20).announce(),
                new MysteryItem(ItemId.MASORI_MASK, 1, 1, 20).announce(),
                new MysteryItem(ItemId.MASORI_BODY, 1, 1, 20).announce(),
                new MysteryItem(ItemId.MASORI_CHAPS, 1, 1, 20).announce(),
                new MysteryItem(ItemId.ZARYTE_CROSSBOW, 1, 1, 20).announce(),
                new MysteryItem(ItemId.ZARYTE_VAMBRACES, 1, 1, 20).announce(),
                new MysteryItem(ItemId.TORVA_FULLHELM_DAMAGED, 1, 1, 20).announce(),
                new MysteryItem(ItemId.TORVA_PLATEBODY_DAMAGED, 1, 1, 20).announce(),
                new MysteryItem(ItemId.TORVA_PLATELEGS_DAMAGED, 1, 1, 20).announce(),
                new MysteryItem(ItemId.CHRISTMAS_CRACKER, 1, 1, 20).announce(),
                new MysteryItem(ItemId.PARTYHAT__SPECS, 1, 1, 20).announce(),
                new MysteryItem(ItemId.TWISTED_BOW, 1, 1, 20).announce(),
                new MysteryItem(ItemId.SCYTHE_OF_VITUR_UNCHARGED, 1, 1, 20).announce(),
                new MysteryItem(ItemId.TUMEKENS_SHADOW_UNCHARGED, 1, 1, 20).announce(),

        };

        totalWeight = MysteryItem.calculateTotalWeight(rewards);

    }

    @Override
    public int[] getItems() {
        return new int[] {32162};
    }
}
