package com.zenyte.plugins.item.mysteryboxes.impl;

import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.item.pluginextensions.ItemPlugin;
import com.zenyte.game.world.entity.npc.drop.viewerentry.DropViewerEntry;
import com.zenyte.game.world.entity.npc.drop.viewerentry.OtherDropViewerEntry;
import com.zenyte.plugins.interfaces.MysteryBoxInterface;
import com.zenyte.plugins.item.mysteryboxes.MysteryItem;
import com.zenyte.plugins.item.mysteryboxes.MysterySupplyItem;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class DonatorWeaponMysteryBox extends ItemPlugin {

    public static int totalWeight;
    public static MysteryItem[] rewards;
    public static MysterySupplyItem[] supplies;

    public static ObjectArrayList<DropViewerEntry> entries = new ObjectArrayList<>();
    public static ObjectArrayList<DropViewerEntry> toEntries() {
        if(entries.size() == 0) {
            calculateEntries();
        }
        return entries;
    }

    private static void calculateEntries() {
        for (final MysteryItem reward : rewards) {
            OtherDropViewerEntry entry = new OtherDropViewerEntry(reward.getId(), reward.getMinAmount(), reward.getMaxAmount(), reward.getWeight(), totalWeight, "");
            entries.add(entry);
        }
    }

    @Override
    public void handle() {
        bind("Open", (player, item, container, slotId) -> MysteryBoxInterface.openBoxQuick(player, item.getId(), rewards, totalWeight));

        rewards = new MysteryItem[] {
                //Common
                new MysteryItem(ItemId.ZAMORAK_GODSWORD, 1, 1, 1000),
                new MysteryItem(ItemId.ABYSSAL_WHIP, 1, 1, 1000),
                new MysteryItem(ItemId.SARADOMIN_SWORD, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_CROSSBOW, 1, 1, 1000),
                new MysteryItem(ItemId.TRIDENT_OF_THE_SEAS_FULL, 1, 1, 1000),
                new MysteryItem(ItemId.LIGHT_BALLISTA, 1, 1, 1000),
                new MysteryItem(ItemId.SARACHNIS_CUDGEL, 1, 1, 1000),
                new MysteryItem(ItemId.THAMMARONS_SCEPTRE_U, 1, 1, 1000),

                //Uncommon
                new MysteryItem(ItemId.ARMADYL_GODSWORD, 1, 1, 500),
                new MysteryItem(ItemId.STAFF_OF_THE_DEAD, 1, 1, 500),
                new MysteryItem(ItemId.ARMADYL_CROSSBOW, 1, 1, 500),
                new MysteryItem(ItemId.ZAMORAKIAN_SPEAR, 1, 1, 500),
                new MysteryItem(ItemId.TANZANITE_FANG, 1, 1, 500),
                new MysteryItem(ItemId.MAGIC_FANG, 1, 1, 500),
                new MysteryItem(ItemId.KRAKEN_TENTACLE, 1, 1, 500),
                new MysteryItem(ItemId.DINHS_BULWARK, 1, 1, 500),
                new MysteryItem(ItemId.ABYSSAL_DAGGER, 1, 1, 500),
                new MysteryItem(ItemId.ABYSSAL_BLUDGEON, 1, 1, 500),
                new MysteryItem(ItemId.HEAVY_BALLISTA, 1, 1, 500),
                new MysteryItem(ItemId.CRAWS_BOW_U, 1, 1, 500),
                new MysteryItem(ItemId.VIGGORAS_CHAINMACE_U, 1, 1, 500),

                //Rare
                new MysteryItem(ItemId.SARADOMIN_GODSWORD, 1, 1, 80),
                new MysteryItem(ItemId.BANDOS_GODSWORD, 1, 1, 80),
                new MysteryItem(ItemId.DRAGON_HUNTER_CROSSBOW, 1, 1, 80),
                new MysteryItem(ItemId.DRAGON_HUNTER_LANCE, 1, 1, 80),
                new MysteryItem(ItemId.DRAGON_WARHAMMER, 1, 1, 80),
                new MysteryItem(ItemId.NIGHTMARE_STAFF, 1, 1, 80),
                new MysteryItem(ItemId.VOLATILE_ORB, 1, 1, 80),
                new MysteryItem(ItemId.ELDRITCH_ORB, 1, 1, 80),
                new MysteryItem(ItemId.ELDER_MAUL, 1, 1, 80),
                new MysteryItem(ItemId.GHRAZI_RAPIER, 1, 1, 80),
                new MysteryItem(ItemId.SANGUINESTI_STAFF_UNCHARGED, 1, 1, 80),
                new MysteryItem(ItemId.VESTAS_LONGSWORD, 1, 1, 80),
                new MysteryItem(ItemId.VESTAS_SPEAR, 1, 1, 80),
                new MysteryItem(ItemId.ZURIELS_STAFF, 1, 1, 80),

                //Really Rare
                new MysteryItem(ItemId.HARMONISED_ORB, 1, 1, 40).announce(),
                new MysteryItem(ItemId.INQUISITORS_MACE, 1, 1, 40).announce(),
                new MysteryItem(ItemId.KODAI_WAND, 1, 1, 40).announce(),
                new MysteryItem(ItemId._3RD_AGE_WAND, 1, 1, 40).announce(),
                new MysteryItem(ItemId._3RD_AGE_LONGSWORD, 1, 1, 40).announce(),
                new MysteryItem(ItemId._3RD_AGE_BOW, 1, 1, 40).announce(),

                //Uber Rare
                new MysteryItem(ItemId.OSMUMTENS_FANG, 1, 1, 5).announce(),
                new MysteryItem(ItemId.ZARYTE_CROSSBOW, 1, 1, 5).announce(),
                new MysteryItem(ItemId.TWISTED_BOW, 1, 1, 5).announce(),
                new MysteryItem(ItemId.SCYTHE_OF_VITUR_UNCHARGED, 1, 1, 5).announce(),
                new MysteryItem(ItemId.TUMEKENS_SHADOW_UNCHARGED, 1, 1, 5).announce(),

        };

        totalWeight = MysteryItem.calculateTotalWeight(rewards);

    }

    @Override
    public int[] getItems() {
        return new int[] {32206};
    }
}
