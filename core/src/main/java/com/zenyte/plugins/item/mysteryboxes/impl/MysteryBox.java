package com.zenyte.plugins.item.mysteryboxes.impl;

import com.zenyte.game.item.ItemId;
import com.zenyte.game.model.item.pluginextensions.ItemPlugin;
import com.zenyte.game.world.entity.npc.drop.viewerentry.DropViewerEntry;
import com.zenyte.game.world.entity.npc.drop.viewerentry.OtherDropViewerEntry;
import com.zenyte.plugins.interfaces.MysteryBoxInterface;
import com.zenyte.plugins.item.mysteryboxes.MysteryItem;
import com.zenyte.plugins.item.mysteryboxes.MysterySupplyItem;
import it.unimi.dsi.fastutil.objects.ObjectArrayList;

/**
 * <AUTHOR> (Discord: astra4)
 */
public class MysteryBox extends ItemPlugin {

    public static int totalWeight;
    public static MysteryItem[] rewards;
    public static MysterySupplyItem[] supplies;

    public static ObjectArrayList<DropViewerEntry> entries = new ObjectArrayList<>();
    public static ObjectArrayList<DropViewerEntry> toEntries() {
        if(entries.size() == 0) {
            calculateEntries();
        }
        return entries;
    }

    private static void calculateEntries() {
        for (final MysteryItem reward : rewards) {
            OtherDropViewerEntry entry = new OtherDropViewerEntry(reward.getId(), reward.getMinAmount(), reward.getMaxAmount(), reward.getWeight(), totalWeight, "");
            entries.add(entry);
        }
    }

    @Override
    public void handle() {
        bind("Open", (player, item, container, slotId) -> MysteryBoxInterface.openBoxQuick(player, item.getId(), rewards, totalWeight));

        rewards = new MysteryItem[] {
                new MysteryItem(ItemId.CRYSTAL_KEY + 1, 1, 10, 1000),
                new MysteryItem(ItemId.DRAGON_AXE, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_BOOTS, 1, 1, 1000),
                new MysteryItem(ItemId.VOLCANIC_WHIP_MIX, 1, 1, 1000),
                new MysteryItem(ItemId.FROZEN_WHIP_MIX, 1, 1, 1000),
                new MysteryItem(ItemId.DRAGON_DEFENDER, 1, 1, 1000).setCheckHasItem(),
                new MysteryItem(ItemId.FIGHTER_TORSO, 1, 1, 1000).setCheckHasItem(),
                new MysteryItem(ItemId.AMULET_OF_FURY, 1, 1, 1000),

                new MysteryItem(ItemId.ONYX, 1, 1, 750),

                new MysteryItem(ItemId.INFINITY_HAT, 1, 1, 500),
                new MysteryItem(ItemId.INFINITY_TOP, 1, 1, 500),
                new MysteryItem(ItemId.INFINITY_BOTTOMS, 1, 1, 500),
                new MysteryItem(ItemId.INFINITY_GLOVES, 1, 1, 500),
                new MysteryItem(ItemId.INFINITY_BOOTS, 1, 1, 500),
                new MysteryItem(ItemId.AMULET_OF_THE_DAMNED_FULL, 1, 1, 500),
                new MysteryItem(ItemId.FIRE_CAPE, 1, 1, 500).setCheckHasItem(),
                new MysteryItem(ItemId.MAGES_BOOK, 1, 1, 500),

                new MysteryItem(ItemId.GUTHANS_HELM, 1, 1, 200),
                new MysteryItem(ItemId.GUTHANS_WARSPEAR, 1, 1, 200),
                new MysteryItem(ItemId.GUTHANS_PLATEBODY, 1, 1, 200),
                new MysteryItem(ItemId.GUTHANS_CHAINSKIRT, 1, 1, 200),
                new MysteryItem(ItemId.VERACS_HELM, 1, 1, 200),
                new MysteryItem(ItemId.VERACS_FLAIL, 1, 1, 200),
                new MysteryItem(ItemId.VERACS_BRASSARD, 1, 1, 200),
                new MysteryItem(ItemId.VERACS_PLATESKIRT, 1, 1, 200),
                new MysteryItem(ItemId.DHAROKS_HELM, 1, 1, 200),
                new MysteryItem(ItemId.DHAROKS_GREATAXE, 1, 1, 200),
                new MysteryItem(ItemId.DHAROKS_PLATEBODY, 1, 1, 200),
                new MysteryItem(ItemId.DHAROKS_PLATELEGS, 1, 1, 200),
                new MysteryItem(ItemId.TORAGS_HELM, 1, 1, 200),
                new MysteryItem(ItemId.TORAGS_HAMMERS, 1, 1, 200),
                new MysteryItem(ItemId.TORAGS_PLATEBODY, 1, 1, 200),
                new MysteryItem(ItemId.TORAGS_PLATELEGS, 1, 1, 200),
                new MysteryItem(ItemId.AHRIMS_HOOD, 1, 1, 200),
                new MysteryItem(ItemId.AHRIMS_STAFF, 1, 1, 200),
                new MysteryItem(ItemId.AHRIMS_ROBETOP, 1, 1, 200),
                new MysteryItem(ItemId.AHRIMS_ROBESKIRT, 1, 1, 200),
                new MysteryItem(ItemId.KARILS_COIF, 1, 1, 200),
                new MysteryItem(ItemId.KARILS_CROSSBOW, 1, 1, 200),
                new MysteryItem(ItemId.KARILS_LEATHERTOP, 1, 1, 200),
                new MysteryItem(ItemId.KARILS_LEATHERSKIRT, 1, 1, 200),
                new MysteryItem(ItemId.ANCIENT_SHARD, 1, 7, 200),

                new MysteryItem(ItemId.AMULET_OF_ETERNAL_GLORY, 1, 1, 100),
                new MysteryItem(ItemId.ABYSSAL_WHIP, 1, 1, 100),
                new MysteryItem(ItemId.SARADOMIN_SWORD, 1, 1, 100),
                new MysteryItem(ItemId.ZAMORAKIAN_SPEAR, 1, 1, 100),
                new MysteryItem(ItemId.DRAGONFIRE_SHIELD_11284, 1, 1, 100),
                new MysteryItem(ItemId.SPIRIT_SHIELD, 1, 1, 100),

                new MysteryItem(ItemId.DRAGON_PICKAXE, 1, 1, 75),
                new MysteryItem(ItemId.DRAGON_CROSSBOW, 1, 1, 75),
                new MysteryItem(ItemId.ENHANCED_ICE_GLOVES, 1, 1, 60),
                new MysteryItem(ItemId.ANCIENT_WYVERN_SHIELD_21634, 1, 1, 42).announce(),
                new MysteryItem(ItemId.BLESSED_SPIRIT_SHIELD, 1, 1, 40),
                new MysteryItem(ItemId.DRAGON_KITESHIELD, 1, 1, 35),
                new MysteryItem(ItemId.DRAGON_PLATEBODY, 1, 1, 35),
                new MysteryItem(ItemId.DRAGON_FULL_HELM, 1, 1, 35),
        };

        totalWeight = MysteryItem.calculateTotalWeight(rewards);

    }

    @Override
    public int[] getItems() {
        return new int[] {6199};
    }
}
