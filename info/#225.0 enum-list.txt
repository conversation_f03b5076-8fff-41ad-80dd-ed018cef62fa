[1715]
inputtype=int
outputtype=string
default=
val=0,Game features
val=1,Tutors
val=2,Training
val=4,Shopping
-----
[2902]
inputtype=int
outputtype=component
default=null
val=0,4:14
val=1,4:15
val=2,4:16
val=3,4:17
-----
[3958]
inputtype=int
outputtype=string
default=null
val=0,35k XP lamp (minimum skill requirement: 60)
val=1,The maximum quantity assigned from the <col=ffffff>Like a boss</col> slayer reward is increased by 25
val=2,When you are assigned a <col=ffffff>TzTok-Jad</col> or <col=ffffff>TzKal-Zuk</col> slayer task you will be assigned to kill 2
val=3,The drop chance of an <col=ffffff>Ecumenical Key</col> is increased by 25%
val=4,The essence required to access a <col=ffffff>God Wars</col> boss is reduced to 25
val=5,You can access a privately rented <col=ffffff>God Wars</col> boss instance for a fee of 100k coins
val=6,Your thralls will last 50% longer
-----
[1056]
inputtype=int
outputtype=namedobj
default=null
val=null,mithril_set_(sk)_13002
val=0,mithril_full_helm_1159
val=1,mithril_platebody_1121
val=2,mithril_plateskirt_1085
val=3,mithril_kiteshield_1197
-----
[3299]
inputtype=int
outputtype=namedobj
default=null
val=0,bunny_ears_1037
val=1,scythe_1419
val=2,war_ship_795
val=3,yo-yo_4079
val=4,rubber_chicken_4566
val=5,zombie_head_6722
val=6,red_marionette_6867
val=7,bobble_hat_6856
val=8,bobble_scarf_6857
val=9,easter_ring_7927
val=10,jack_lantern_mask_9920
val=11,skeleton_mask_9925
val=12,reindeer_hat_10507
val=13,chicken_head_11021
val=14,black_h'ween_mask_11847
val=15,black_partyhat_11862
val=16,rainbow_partyhat_11863
val=17,cow_mask_11919
val=18,easter_basket_4565
val=19,druidic_wreath_12600
val=20,grim_reaper_hood_12845
val=21,santa_mask_12887
val=22,antisanta_mask_12892
val=23,bunny_top_13663
val=24,mask_of_balance_13203
val=25,tiger_toy_13215
val=26,anti-panties_13288
val=27,gravedigger_mask_13283
val=28,black_santa_hat_13343
val=29,inverted_santa_hat_13344
val=30,gnome_child_hat_13655
val=31,cabbage_cape_13679
val=32,cruciferous_codex_13681
val=33,hornwood_helm_19699
val=34,banshee_mask_20773
val=35,snow_globe_20832
val=36,sack_of_presents_20834
val=37,giant_present_20836
val=38,4th_birthday_hat_21211
val=39,birthday_balloons_21209
val=40,easter_egg_helm_21214
val=41,eggshell_platebody_22351
val=42,rainbow_scarf_21314
val=43,hand_fan_21354
val=44,runefest_shield_21695
val=45,jonas_mask_21720
val=46,snow_imp_costume_head_21847
val=47,wise_old_man's_santa_hat_21859
val=48,prop_sword_22316
val=49,clown_mask_22689
val=50,eek_22684
val=51,star-face_22713
val=52,birthday_cake_23108
val=53,giant_easter_egg_23446
val=54,bunnyman_mask_23448
val=55,spooky_hood_24305
val=56,spookier_hood_24315
val=57,pumpkin_lantern_24325
val=58,skeleton_lantern_24327
val=59,green_gingerbread_shield_24428
val=60,cat_ears_24525
val=61,magic_egg_ball_24535
val=62,carrot_sword_24537
val=63,headless_head_24975
val=64,magical_pumpkin_24977
val=65,giant_boulder_25314
val=66,goblin_decorations_25316
val=67,20th_anniversary_hat_25322
val=68,gnome_child_mask_25336
val=69,cursed_banana_25500
val=70,banana_cape_25502
val=71,banana_hat_25840
val=72,gregg's_eastdoor_25604
val=73,propeller_hat_25606
val=74,saucepan_26254
val=75,dragon_hunter_gloves_26256
val=76,haunted_wine_bottle_26260
val=77,snowman_ring_26314
val=78,secret_santa_present_(red)_26316
val=79,festive_elf_slippers_26310
val=80,festive_elf_hat_26312
val=81,skis_26649
val=82,easter_hat_26937
val=83,crate_ring_26939
val=84,flower_crown_27035
val=85,treat_cauldron_27463
val=86,witch_hat_27473
val=87,halloween_wig_27497
val=88,santa's_list_27564
val=89,christmas_jumper_27566
val=90,snow_goggles_&_hat_27568
val=91,sack_of_coal_27570
val=92,festive_nutcracker_hat_27576
val=93,festive_games_crown_27588
val=94,mystic_cards_27645
val=95,10th_birthday_cape_27812
val=96,cake_hat_27804
val=97,silver_partyhat_27828
val=98,bob_the_cat_slippers_27806
val=99,oldschool_jumper_27822
val=100,dragon_candle_dagger_27810
val=101,10th_birthday_balloons_27820
val=102,gnome_child_plush_27818
val=103,gnome_child_backpack_27802
-----
[2112]
inputtype=int
outputtype=namedobj
default=null
val=0,tyrannical_ring_12603
val=1,dragon_pickaxe_11920
val=2,dragon_2h_sword_7158
val=3,claws_of_callisto_27667
val=4,voidwaker_hilt_27681
-----
[4220]
inputtype=int
outputtype=obj
default=null
val=0,shoe_26662
val=1,shoe_26663
val=2,shoe_26664
val=3,shoe_26665
val=4,shoe_26666
val=5,shoe._26667
-----
[4089]
inputtype=struct
outputtype=int
default=0
val=3334,1
-----
[3168]
inputtype=int
outputtype=namedobj
default=null
val=0,attack_cape_9747
val=1,attack_hood_9749
-----
[10024]
inputtype=int
outputtype=int
default=null
val=0,565
val=1,560
val=2,9075
val=3,557
val=4,555
val=5,562
val=6,566
val=7,554
val=8,556
val=9,561
val=10,563
val=11,564
val=12,21880
val=13,3144
val=14,385
val=15,391
val=16,397
val=17,13441
val=18,11936
val=19,6685
val=20,10925
val=21,3024
val=22,2434
val=23,2440
val=24,2442
val=25,2436
val=26,12695
val=27,2444
val=28,3040
val=29,4417
val=30,11090
val=31,2550
val=32,5698
val=33,24225
val=34,10887
val=35,11802
val=36,20784
-----
[1449]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=1_1_0,skeleton_guard_8131
val=1_2_0,guard_dog_8132
val=1_3_0,hobgoblin_guard_8133
val=1_4_0,baby_red_dragon_8134
val=1_5_0,huge_spider_8135
val=1_6_0,troll_guard_8136
val=1_7_0,hellhound_8137
val=2_1_0,spike_trap_8143
val=2_2_0,man_trap_8144
val=2_3_0,tangle_vine_8145
val=2_4_0,marble_trap_8146
val=2_5_0,teleport_trap_8147
val=3_1_0,spike_trap_8143
val=3_2_0,man_trap_8144
val=3_3_0,tangle_vine_8145
val=3_4_0,marble_trap_8146
val=3_5_0,teleport_trap_8147
val=4_1_0,oak_door_8122
val=4_2_0,steel-plated_door_8123
val=4_3_0,marble_door_8124
val=5_1_0,oak_door_8122
val=5_2_0,steel-plated_door_8123
val=5_3_0,marble_door_8124
val=6_1_0,candles_8128
val=6_2_0,torches_8129
val=6_3_0,skull_torches_8130
val=7_1_0,decorative_blood_8125
val=7_2_0,decorative_pipe_8126
val=7_3_0,hanging_skeleton_8127
-----
[1981]
inputtype=int
outputtype=enum
default=null
val=0,1982
val=1,1983
val=2,1984
val=3,1985
-----
[10028]
inputtype=int
outputtype=int
default=null
val=0,10315
val=1,10316
val=2,10317
val=3,10318
-----
[4224]
inputtype=int
outputtype=struct
default=null
val=null,4209
val=0,4210
val=1,4213
val=2,4211
val=3,4212
-----
[3164]
inputtype=int
outputtype=namedobj
default=null
val=0,zombie_mask_7594
val=1,zombie_shirt_7592
val=2,zombie_trousers_7593
val=3,zombie_gloves_7595
val=4,zombie_boots_7596
-----
[1453]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=1_1_0,exit_portal_8168
val=1_2_0,decorative_rock_8169
val=1_3_0,pond_8170
val=1_4_0,imp_statue_8171
val=1_5_0,dungeon_entrance_8172
val=2_1_0,tree_8173
val=2_2_0,nice_tree_8174
val=2_3_0,oak_tree_8175
val=2_4_0,willow_tree_8176
val=2_5_0,maple_tree_8177
val=2_6_0,yew_tree_8178
val=2_7_0,magic_tree_8179
val=3_1_0,tree_8173
val=3_2_0,nice_tree_8174
val=3_3_0,oak_tree_8175
val=3_4_0,willow_tree_8176
val=3_5_0,maple_tree_8177
val=3_6_0,yew_tree_8178
val=3_7_0,magic_tree_8179
val=4_1_0,fern_8186
val=4_2_0,bush_8187
val=4_3_0,tall_plant_8188
val=4_4_0,pumpkin_24979
val=5_1_0,short_plant_8189
val=5_2_0,large-leaf_plant_8190
val=5_3_0,huge_plant_8191
val=5_4_0,pumpkin_24979
val=6_1_0,plant_8180
val=6_2_0,small_fern_8181
val=6_3_0,fern_8182
val=7_1_0,dock_leaf_8183
val=7_2_0,thistle_8184
val=7_3_0,reeds_8185
val=8_1_0,tip_jar_20634
-----
[790]
inputtype=int
outputtype=string
default=Colourful
val=0,Khaki
val=1,Charcoal
val=2,Crimson
val=3,Navy
val=4,Straw
val=5,White
val=6,Red
val=7,Blue
val=8,Green
val=9,Yellow
val=10,Purple
val=11,Orange
val=12,Rose
val=13,Lime
val=14,Cyan
val=15,Emerald
val=16,Black
val=17,Grey
val=18,Onion
val=19,Peach
val=20,Lumbridge Blue
val=21,Deep Blue
val=22,Light Pink
val=23,Cadmium red
val=24,Maroon
val=25,Pale green
val=26,Turquoise
val=27,Deep purple
val=28,Light purple
-----
[2636]
inputtype=int
outputtype=int
default=0
val=10,1
val=11,10
val=12,50
val=13,100
val=14,500
val=15,1000
-----
[3561]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=1_1_0,trophy_pedestal_25351
val=1_2_0,ornate_trophy_pedestal_25352
val=2_1_0,trophy_pedestal_25351
val=2_2_0,ornate_trophy_pedestal_25352
val=3_1_0,trophy_pedestal_25351
val=3_2_0,ornate_trophy_pedestal_25352
val=4_1_0,rug_25362
val=4_2_0,opulent_rug_25363
val=4_3_0,trailblazer_rug_25364
val=5_1_0,oak_trophy_case_25353
val=5_2_0,mahogany_trophy_case_25354
val=6_1_0,banner_stand_25355
val=6_2_0,ornate_banner_stand_25356
val=7_1_0,oak_outfit_stand_25357
val=7_2_0,mahogany_outfit_stand_25358
val=8_1_0,league_statue_25359
val=8_2_0,ornate_league_statue_25360
val=8_3_0,trailblazer_globe_25361
val=9_1_0,league_accomplishments_scroll_25365
-----
[3827]
inputtype=int
outputtype=struct
default=null
val=null,2953
val=0,2954
val=1,2955
val=2,2956
val=3,2957
val=4,2958
val=5,2959
val=6,2960
val=7,2961
-----
[3823]
inputtype=int
outputtype=string
default=If by my life or death I can protect you, I will. You have my sword.
val=0,
val=1,Timelessly fashionable. The helmet of a champion.
val=2,Not all who wander are lost.
val=3,A good tool improves the way you work. A great tool improves the way you think.
val=4,Party time! Excellent.
val=5,Ashes to ashes. Dust to dust. Bones to experience.
val=6,It is our choices that show us what we truly are. Far more than our abilities.
val=7,Go in peace in the name of Saradomin; may his glory shine upon you like the sun.
val=8,Give a noob a fish and they will leave you alone. Teach a noob to fish and they will deflate the fish market.
val=9,Life's biggest tragedy is that we get old too soon and wise too late.
val=10,Follow it. You'll be happier.
val=11,Rise and rise again, until lambs become lions.
val=12,May your bloodthirst never be sated, and may all your battles be glorious. Zamorak bring you strength.
val=13,Life is a garden. You reap what you sow.
val=14,A day without laughter is a day wasted.
val=15,If by my life or death I can protect you, I will. You have my sword.
val=16,Aim steady. Perfection is unattainable, but one should always strive for it.
val=17,May you walk the path, and never fall, for Guthix walks beside thee on thy journey. May Guthix bring you peace.
val=18,Pawschamp?
val=19,Cats are easily as tough as sheep. Cats were the second thing Guthix brought here, after sheep of course.
val=20,Stand and deliver!
val=21,The shield that guards the realms of men.
val=22,Brassica Prime. Thy Name be Vegetable, thy land be soil. Plant in us thy seeds of faith, that we may grow beyond allotments. Forever, your crops. Cabbage.
val=23,A rose in the grey.
val=24,You must really like your clan if you put a ring on it.
val=25,A risky life, to be marked by Him. Though a skulled adventurer need not fear when their clan is near.
val=26,By the way.
val=27,Ultimately, you stand alone.
-----
[794]
inputtype=int
outputtype=namedobj
default=null
val=0,empty_sack_10486
val=1,undead_chicken_10487
val=2,selected_iron_10488
val=3,bar_magnet_10489
val=4,undead_twigs_10490
val=5,research_notes_10492
val=6,translated_notes_10493
val=7,a_pattern_10494
val=8,a_container_10495
val=9,crone-made_amulet_10500
-----
[1052]
inputtype=int
outputtype=namedobj
default=null
val=null,black_trimmed_set_(sk)_12994
val=0,black_full_helm_(t)_2587
val=1,black_platebody_(t)_2583
val=2,black_plateskirt_(t)_3472
val=3,black_kiteshield_(t)_2589
-----
[3962]
inputtype=int
outputtype=string
default=null
val=0,<col=ffffff>Ghommal's hilt 6</col>
val=1,Daily Trollheim teleports - Unlimited
val=2,Daily Mor Ul Rek teleports - Unlimited
-----
[3954]
inputtype=int
outputtype=string
default=null
val=0,The ability to combine <col=ffffff>KBD Heads</col> with a <col=ffffff>Dragon Hunter Crossbow</col> to recolour it
-----
[3557]
inputtype=int
outputtype=namedobj
default=null
val=0,trailblazer_hood_(t1)_25028
val=1,trailblazer_top_(t1)_25031
val=2,trailblazer_trousers_(t1)_25034
val=3,trailblazer_boots_(t1)_25037
-----
[1711]
inputtype=int
outputtype=namedobj
default=null
val=0,olmlet_20851
val=1,vespina_22384
val=2,vasa_minirio_22382
val=3,vanguard_22380
val=4,puppadile_22376
val=5,tektiny_22378
val=6,enraged_tektiny_24656
val=7,flying_vespina_24658
-----
[1060]
inputtype=int
outputtype=namedobj
default=null
val=null,mithril_gold-trimmed_set_(sk)_13010
val=0,mithril_full_helm_(g)_12283
val=1,mithril_platebody_(g)_12277
val=2,mithril_plateskirt_(g)_12285
val=3,mithril_kiteshield_(g)_12281
-----
[2116]
inputtype=int
outputtype=namedobj
default=null
val=0,armadyl_crossbow_11785
val=1,saradomin_hilt_11814
val=2,saradomin_sword_11838
val=3,saradomin's_light_13256
val=4,godsword_shard_1_11818
val=5,godsword_shard_2_11820
val=6,godsword_shard_3_11822
-----
[3037]
inputtype=int
outputtype=struct
default=null
val=0,1265
-----
[2501]
inputtype=int
outputtype=int
default=null
val=0,5
val=1,11
val=2,5
val=3,5
-----
[4347]
inputtype=int
outputtype=location
default=null
val=0,null_14135
val=1,null_14134
val=2,null_14135
val=3,null_14134
val=4,null_14133
val=5,null_14134
val=6,null_14135
val=7,null_14134
val=8,null_14135
-----
[4351]
inputtype=int
outputtype=location
default=null
val=0,null_14150
val=1,null_14149
val=2,null_14150
val=3,null_14149
val=4,null_14148
val=5,null_14149
val=6,null_14150
val=7,null_14149
val=8,null_14150
-----
[4359]
inputtype=int
outputtype=location
default=null
val=0,null_14081
val=1,null_14080
val=2,null_14081
val=3,null_14080
val=4,null_11703
val=5,null_14080
val=6,null_14081
val=7,null_14080
val=8,null_14081
-----
[2513]
inputtype=int
outputtype=int
default=null
val=0,197
val=1,199
val=2,198
val=3,203
val=4,200
val=5,201
val=6,202
val=7,212
val=8,214
val=9,208
val=10,211
val=11,213
val=12,207
val=13,210
val=14,209
val=15,205
val=16,204
val=17,206
val=18,216
val=19,217
val=20,215
val=21,220
val=22,221
-----
[2505]
inputtype=int
outputtype=int
default=null
val=0,5
val=1,10
val=2,8
val=3,3
-----
[2509]
inputtype=int
outputtype=int
default=null
val=0,11
val=1,8
val=2,6
val=3,5
-----
[9500]
inputtype=int
outputtype=struct
default=0
val=0,9500
val=1,9501
val=2,9502
val=3,9503
val=4,9504
val=5,9505
val=6,9506
val=7,9507
val=8,9508
val=9,9509
val=10,9510
val=11,9511
val=12,9512
val=13,9513
val=14,9514
val=15,9515
val=16,9516
val=17,9517
val=18,9518
val=19,9519
val=20,9520
val=21,9521
val=22,9522
val=23,9523
val=24,9524
val=25,9525
val=26,9526
val=27,9527
val=28,9528
val=29,9529
val=30,9530
val=31,9531
val=32,9532
val=33,9533
val=34,9534
val=35,9535
val=36,9536
val=37,9537
val=38,9538
val=39,9539
val=40,9540
val=41,9541
val=42,9542
val=43,9543
val=44,9544
val=45,9545
val=46,9546
val=47,9547
val=48,9548
val=49,9549
val=50,9550
val=51,9551
val=52,9552
val=53,9553
val=54,9554
val=55,9555
val=56,9556
val=57,9557
val=58,9558
val=59,9559
val=60,9560
val=61,9561
val=62,9562
val=63,9563
val=64,9564
val=65,9565
val=66,9566
val=67,9567
val=68,9568
val=69,9569
val=70,9570
val=71,9571
val=72,9572
val=73,9573
val=74,9574
val=75,9575
val=76,9576
val=77,9577
val=78,9578
val=79,9579
val=80,9580
-----
[4355]
inputtype=int
outputtype=location
default=null
val=0,null_12130
val=1,null_12129
val=2,null_12130
val=3,null_12129
val=4,null_12128
val=5,null_12129
val=6,null_12130
val=7,null_12129
val=8,null_12130
-----
[2108]
inputtype=namedobj
outputtype=namedobj
default=null
val=farmer's_boro_trousers_13640,farmer's_boro_trousers_13641
val=farmer's_jacket_13642,farmer's_shirt_13643
val=farmer's_boots_13644,farmer's_boots_13645
val=farmer's_strawhat_13646,farmer's_strawhat_13647
-----
[3033]
inputtype=int
outputtype=struct
default=null
val=0,693
val=1,694
val=2,695
val=3,1385
-----
[3029]
inputtype=int
outputtype=struct
default=null
val=0,1388
val=1,1389
val=2,1390
-----
[2104]
inputtype=int
outputtype=struct
default=0
val=0,507
val=1,506
val=2,4378
-----
[3950]
inputtype=int
outputtype=string
default=null
val=0,10k XP lamp (minimum skill requirement: 30)
val=1,The drop chance of a <col=ffffff>Clue Scroll (Medium)</col> from all sources (excl. implings) is increased by 5%
val=2,The maximum quantity assigned from the <col=ffffff>Like a boss</col> slayer reward is increased by 10
val=3,200% bonus to tokens earned from <col=ffffff>Warrior Guild</col> activities
val=4,2 additional commendation points from successful <col=ffffff>Pest Control</col> games
val=5,Immunity from the prayer draining effect at <col=ffffff>Barrows</col> when one of <col=ffffff>Ghommal's hilts</col> is equipped
val=6,The <col=ffffff>Dwarf multicannon</col> can hold 5 extra cannonballs
-----
[4625]
inputtype=int
outputtype=int
default=0
val=0,0
-----
[2779]
inputtype=int
outputtype=struct
default=null
val=0,1256
val=1,1257
val=2,1258
val=3,1259
val=4,1260
val=5,1261
val=6,1262
-----
[139]
inputtype=component
outputtype=component
default=null
val=161:1,80:4
val=161:2,80:6
val=161:3,80:8
val=161:4,80:17
val=161:5,80:38
val=161:6,80:39
val=161:7,80:5
val=161:8,80:7
val=161:9,80:20
val=161:10,80:15
val=161:11,80:18
val=161:12,80:19
val=161:13,80:40
val=161:14,80:10
val=161:15,80:11
val=161:16,80:12
val=161:17,80:13
val=161:32,80:21
val=161:33,80:1
val=161:34,80:43
val=161:35,80:41
val=161:36,80:42
val=161:73,80:23
val=161:75,80:24
val=161:76,80:25
val=161:77,80:26
val=161:78,80:27
val=161:79,80:28
val=161:80,80:29
val=161:81,80:30
val=161:82,80:31
val=161:83,80:32
val=161:84,80:33
val=161:85,80:34
val=161:86,80:35
val=161:87,80:36
val=161:88,80:37
val=161:90,80:3
val=161:91,80:16
val=161:92,80:9
val=161:94,80:22
-----
[3553]
inputtype=int
outputtype=namedobj
default=null
val=0,soul_cape_25346
val=1,ectoplasmator_25340
val=2,spoils_of_war_25342
val=3,blighted_bind_sack_24609
val=4,blighted_snare_sack_24611
val=5,blighted_entangle_sack_24613
val=6,blighted_teleport_spell_sack_24615
val=7,blighted_vengeance_sack_24621
val=8,blighted_ancient_ice_sack_24607
val=9,blighted_manta_ray_24590
val=10,blighted_anglerfish_24593
val=11,blighted_karambwan_24596
val=12,blighted_super_restore(4)_24599
-----
[3422]
inputtype=int
outputtype=int
default=0
val=3,1
val=4,1
val=19,1
val=29,1
val=30,1
val=39,1
val=40,1
val=41,1
val=42,1
val=61,1
val=62,1
val=63,1
val=74,1
val=75,1
-----
[274]
inputtype=int
outputtype=string
default=out and about somewhere
val=1,in your bank
val=2,in your inventory
val=3,your follower
val=4,in your menagerie
-----
[3966]
inputtype=int
outputtype=component
default=null
val=0,715:30
val=1,715:31
val=2,715:32
val=3,715:33
-----
[2120]
inputtype=int
outputtype=namedobj
default=null
val=0,bandos_chestplate_11832
val=1,bandos_tassets_11834
val=2,bandos_boots_11836
val=3,bandos_hilt_11812
val=4,godsword_shard_1_11818
val=5,godsword_shard_2_11820
val=6,godsword_shard_3_11822
-----
[1985]
inputtype=int
outputtype=obj
default=null
val=0,null_21835
val=1,easter_11997
val=2,null_2885
val=3,null_6208
val=4,null_6888
val=5,null_20409
val=6,null_20410
val=7,null_20411
val=8,null_20412
val=9,null_20413
val=10,null_20414
val=11,null_20419
val=12,null_20420
val=13,null_20404
val=14,null_20427
val=15,null_8092
val=16,null_20391
val=17,null_22779
val=18,null_20398
val=19,null_20399
val=20,null_20400
val=21,null_20392
val=22,null_21826
val=23,null_21829
val=24,null_21832
val=25,null_20763
val=26,null_10511
val=27,null_20762
val=28,null_15346
val=29,null_8796
val=30,null_15345
val=31,null_15344
val=32,null_15304
val=33,null_15303
val=34,null_15309
val=35,null_25511
val=36,null_25509
val=37,null_25508
val=38,null_25506
val=39,null_25512
val=40,null_25513
val=41,null_25507
val=42,null_25510
val=43,null_25514
-----
[3160]
inputtype=int
outputtype=namedobj
default=null
val=0,vyre_noble_top_24676
val=1,vyre_noble_legs_24678
val=2,vyre_noble_shoes_24680
-----
[4232]
inputtype=int
outputtype=string
default=Reward
val=0,Blighted Wave sack
val=1,Blighted Surge sack
val=2,Scroll of Imbuing
-----
[3156]
inputtype=int
outputtype=namedobj
default=null
val=0,twisted_hat_(t1)_24405
val=1,twisted_coat_(t1)_24407
val=2,twisted_trousers_(t1)_24409
val=3,twisted_boots_(t1)_24411
-----
[3815]
inputtype=int
outputtype=string
default=
val=null,Duration (Days)
-----
[1989]
inputtype=int
outputtype=namedobj
default=null
val=0,fire_4653
val=1,etchings_4654
val=2,translation_4655
val=3,silver_pot_4658
val=4,blessed_pot_4659
val=5,silver_pot_4660
val=6,blessed_pot_4661
val=7,silver_pot_4662
val=8,blessed_pot_4663
val=9,silver_pot_4664
val=10,blessed_pot_4665
val=11,silver_pot_4666
val=12,blessed_pot_4667
val=13,garlic_powder_4668
val=14,blood_diamond_4670
val=15,ice_diamond_4671
val=16,smoke_diamond_4672
val=17,shadow_diamond_4673
val=18,gilded_cross_4674
-----
[4228]
inputtype=int
outputtype=struct
default=null
val=null,4229
val=0,3106
val=1,4230
val=2,4233
val=3,4231
val=4,4234
val=5,4232
-----
[3831]
inputtype=int
outputtype=struct
default=null
val=null,2968
val=0,2969
val=1,2970
val=2,4387
val=3,4388
val=4,4389
-----
[3819]
inputtype=int
outputtype=struct
default=2937
val=0,2935
val=1,2936
val=2,2937
val=3,2938
val=4,2939
val=5,2940
val=6,2941
val=7,2942
-----
[4605]
inputtype=int
outputtype=null
default=null
val=0,1821
val=1,1824
val=2,1825
val=3,1832
val=4,1835
val=5,1827
val=6,1831
val=7,1833
val=8,1829
val=9,1820
val=10,1828
val=11,1826
val=12,1819
val=13,1834
val=14,1842
val=15,1837
val=16,1840
val=17,1841
val=18,1838
val=19,1830
val=20,1839
val=21,1823
val=22,1836
val=23,1822
-----
[1727]
inputtype=int
outputtype=namedobj
default=null
val=1,wilderness_cape_21428
val=2,wilderness_cape_21429
val=4,wilderness_cape_21430
val=8,wilderness_cape_21431
val=16,wilderness_cape_21432
-----
[4629]
inputtype=int
outputtype=namedobj
default=null
val=0,null
val=1,mystic_robe_top_20425
val=2,rune_platelegs_20422
val=3,amulet_of_glory_20586
val=4,helm_of_neitiznot_23591
val=5,barrows_gloves_23593
val=6,climbing_boots_20578
val=7,abyssal_whip_20405
val=8,imbued_guthix_cape_23603
val=9,dragon_defender_23597
val=10,berserker_ring_23595
val=11,diamond_bolts_(e)_23649
val=12,black_d'hide_body_20423
val=13,mystic_robe_bottom_20426
val=14,spirit_shield_23599
val=15,ahrim's_staff_23653
val=16,rune_crossbow_23601
val=17,dragon_dagger_20407
val=18,saradomin_brew(4)_23575
val=19,super_restore(4)_23567
val=20,sanfew_serum(4)_23559
val=21,ranging_potion(4)_23551
val=22,super_combat_potion(4)_23543
val=23,shark_20390
val=24,cooked_karambwan_23533
val=25,rune_pouch_23650
val=26,imbued_zamorak_cape_23605
val=27,imbued_saradomin_cape_23607
val=28,mystic_robe_top_(dark)_27158
val=29,mystic_robe_bottom_(dark)_27159
val=30,mystic_robe_top_(light)_27160
val=31,mystic_robe_bottom_(light)_27161
val=32,tome_of_fire_27358
-----
[2362]
inputtype=stat
outputtype=string
default=a
val=0,an
val=16,an
-----
[2759]
inputtype=obj
outputtype=namedobj
default=null
val=apprentice_wand_6910,beginner_wand_6908
val=teacher_wand_6912,apprentice_wand_6910
val=master_wand_6914,teacher_wand_6912
-----
[4216]
inputtype=int
outputtype=struct
default=null
val=0,4160
val=1,4161
val=2,4162
val=3,4163
val=4,4164
val=5,4165
val=6,4166
val=7,4167
val=8,4168
val=9,4169
val=10,4170
val=11,4171
val=12,4172
val=13,4173
val=14,4174
val=15,4175
val=16,4176
val=17,4177
val=18,4178
val=19,4179
val=20,4180
val=21,4181
val=22,4182
val=23,4183
val=24,4184
val=25,4185
val=26,4186
val=27,4187
val=28,4188
val=29,4189
val=30,4190
val=31,4191
val=32,4192
val=33,4193
val=34,4194
val=35,4195
val=36,4196
val=37,4197
val=38,4198
val=39,4199
val=40,4200
val=41,4201
val=42,4202
val=43,4203
val=44,4204
val=45,4205
val=46,4206
val=47,4207
val=48,4208
-----
[3172]
inputtype=int
outputtype=namedobj
default=null
val=0,agility_cape_9771
val=1,agility_hood_9773
-----
[909]
inputtype=int
outputtype=enum
default=null
val=1,3269
val=2,3254
val=3,2629
-----
[917]
inputtype=int
outputtype=string
default=
val=null,Magic
val=0,All spellbooks
val=1,Standard spells
val=2,Binding only
val=3,Disabled
-----
[3176]
inputtype=int
outputtype=namedobj
default=null
val=0,farming_cape_9810
val=1,farming_hood_9812
-----
[4208]
inputtype=int
outputtype=string
default=Standard Spellbook
val=1,Ancient Magicks
val=2,Lunar Spellbook
val=3,Arceuus Spellbook
-----
[933]
inputtype=enum
outputtype=graphic
default=null
val=913,197
val=915,200
val=917,202
val=919,201
val=921,212
val=923,223
val=925,224
val=2336,225
val=2338,226
val=2340,227
-----
[3180]
inputtype=int
outputtype=namedobj
default=null
val=0,herblore_cape_9774
val=1,herblore_hood_9776
-----
[913]
inputtype=int
outputtype=string
default=
val=null,Melee
val=0,Allowed
val=1,Disabled
-----
[929]
inputtype=int
outputtype=string
default=
val=null,Arena
val=0,Wasteland
val=1,Plateau
val=2,Sylvan Glade
val=3,Forsaken Quarry
val=4,Turrets
val=5,Clan Cup Arena
val=6,Soggy Swamp
val=7,Ghastly Swamp
val=8,Northleach Quell
val=9,Gridlock
val=10,Ethereal
val=11,Classic
val=12,Lumbridge Castle
val=13,Falador Park
-----
[925]
inputtype=int
outputtype=string
default=
val=null,Special Attacks
val=0,Allowed
val=1,No Staff of the Dead
val=2,Disabled
-----
[2775]
inputtype=int
outputtype=component
default=null
val=0,387:15
val=1,387:16
val=2,387:17
val=3,387:18
val=4,387:19
val=5,387:20
val=7,387:21
val=9,387:22
val=10,387:23
val=12,387:24
val=13,387:25
-----
[4621]
inputtype=int
outputtype=component
default=null
val=0,421:18
val=1,421:19
val=2,421:20
val=3,421:21
val=4,421:22
val=5,421:23
val=7,421:24
val=9,421:25
val=10,421:26
val=12,421:27
val=13,421:28
-----
[921]
inputtype=int
outputtype=string
default=
val=null,Food
val=0,Allowed
val=1,Disabled
-----
[1072]
inputtype=int
outputtype=namedobj
default=null
val=null,rune_gold-trimmed_set_(sk)_13034
val=0,rune_full_helm_(g)_2619
val=1,rune_platebody_(g)_2615
val=2,rune_plateskirt_(g)_3476
val=3,rune_kiteshield_(g)_2621
-----
[4601]
inputtype=int
outputtype=int
default=1
val=0,0
-----
[1171]
inputtype=int
outputtype=int
default=100
val=1,150
val=2,75
-----
[3414]
inputtype=int
outputtype=string
default=
val=null,Area
val=0,All
val=1,Misthalin
val=2,Karamja
val=3,Asgarnia
val=4,Kandarin
val=5,Morytania
val=6,Desert
val=7,Tirannwn
val=8,Fremennik
val=9,Wilderness
val=10,Kourend
val=11,Non-Specific
-----
[2755]
inputtype=obj
outputtype=int
default=null
val=death_rune_560,2
val=nature_rune_561,0
val=chaos_rune_562,0
val=law_rune_563,2
val=cosmic_rune_564,0
val=blood_rune_565,2
val=soul_rune_566,2
val=steam_rune_4694,1
val=mist_rune_4695,1
val=dust_rune_4696,1
val=smoke_rune_4697,1
val=mud_rune_4698,1
val=lava_rune_4699,1
val=mage's_book_6889,500
val=beginner_wand_6908,30
val=apprentice_wand_6910,60
val=teacher_wand_6912,150
val=master_wand_6914,240
val=infinity_top_6916,400
val=infinity_hat_6918,350
val=infinity_boots_6920,120
val=infinity_gloves_6922,175
val=infinity_bottoms_6924,450
val=bones_to_peaches_6926,200
-----
[3843]
inputtype=int
outputtype=component
default=null
val=1,162:10
val=2,162:14
val=3,162:18
val=4,162:22
val=5,162:26
val=6,162:30
-----
[1997]
inputtype=int
outputtype=namedobj
default=null
val=0,slashed_book_9715
val=1,a_stone_bowl_2888
val=2,a_stone_bowl_2889
val=3,rock_9716
-----
[3152]
inputtype=int
outputtype=namedobj
default=null
val=0,ornate_helm_23101
val=1,ornate_top_23097
val=2,ornate_legs_23095
val=3,ornate_cape_23099
val=4,ornate_gloves_23091
val=5,ornate_boots_23093
-----
[3049]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=1_1_0,wooden_table_24890
val=1_2_0,oak_table_24891
val=1_3_0,teak_table_24892
val=1_4_0,mahogany_table_24893
val=2_1_0,wooden_bookcase_24902
val=2_2_0,oak_bookcase_24903
val=2_3_0,teak_bookcase_24904
val=2_4_0,mahogany_bookcase_24905
val=3_1_0,wooden_shelves_24914
val=3_2_0,oak_shelves_24915
val=3_3_0,teak_shelves_24916
val=3_4_0,mahogany_shelves_24917
val=4_1_0,wooden_bed_24922
val=4_2_0,oak_bed_24923
val=4_3_0,teak_bed_24924
val=4_4_0,mahogany_bed_24925
val=5_1_0,wooden_drawer_24926
val=5_2_0,oak_drawer_24927
val=5_3_0,teak_drawer_24928
val=5_4_0,mahogany_drawer_24929
val=6_1_0,wooden_dresser_24910
val=6_2_0,oak_dresser_24911
val=6_3_0,teak_dresser_24912
val=6_4_0,mahogany_dresser_24913
val=7_1_0,null_8245
val=8_1_0,wooden_chair_24930
val=8_2_0,oak_chair_24931
val=8_3_0,teak_chair_24932
val=8_4_0,mahogany_chair_24933
-----
[10044]
inputtype=int
outputtype=int
default=null
val=0,*********
val=1,*********
val=2,*********
val=3,*********
val=4,*********
val=5,*********
val=6,*********
val=7,*********
val=8,*********
val=9,*********
val=10,*********
val=11,*********
val=12,*********
val=13,*********
val=14,*********
val=15,*********
val=16,*********
val=17,*********
val=18,*********
val=19,*********
val=20,*********
val=21,*********
val=22,*********
val=23,*********
val=24,*********
-----
[3581]
inputtype=int
outputtype=string
default=null
val=0,HasBankKey
val=1,personal rex
val=2,Bas Furret
val=3,UIM Herblore
val=4,Crowride1873
-----
[3811]
inputtype=int
outputtype=string
default=
val=null,Activity
val=0,Revenants
val=1,Deep Wilderness
val=2,Chaos Altar
val=3,Last Man Standing
val=4,Soul Wars
val=5,Castle Wars
val=6,Dharoks
val=7,1 Defence
val=8,Berserker
val=9,Med-Level
val=10,Free to play
val=11,Rimmington
val=12,Camelot
val=13,Lumbridge
val=14,Grand Exchange
val=15,Edgeville
val=16,Ferox Enclave
val=17,Risk-free
-----
[4335]
inputtype=int
outputtype=location
default=null
val=0,null_14093
val=1,null_14092
val=2,null_14093
val=3,null_14092
val=4,null_14091
val=5,null_14092
val=6,null_14093
val=7,null_14092
val=8,null_14093
-----
[1040]
inputtype=int
outputtype=namedobj
default=null
val=null,bronze_gold-trimmed_set_(sk)_12970
val=0,bronze_full_helm_(g)_12211
val=1,bronze_platebody_(g)_12205
val=2,bronze_plateskirt_(g)_12209
val=3,bronze_kiteshield_(g)_12213
-----
[3315]
inputtype=int
outputtype=string
default=Select
val=0,Brown
val=1,Khaki
val=2,Ashen
val=3,Dark
val=4,Terracotta
val=5,Grey
-----
[377]
inputtype=int
outputtype=int
default=0
val=9,80
val=10,250
val=11,750
val=12,1500
val=13,2500
val=14,4000
val=15,6000
-----
[4240]
inputtype=int
outputtype=namedobj
default=null
val=0,hat_of_the_eye_(red)_26858
val=1,robe_top_of_the_eye_(red)_26860
val=2,robe_bottoms_of_the_eye_(red)_26862
val=3,boots_of_the_eye_26856
-----
[1469]
inputtype=int
outputtype=model
default=null
val=0,10245
val=1,10246
val=2,10247
val=3,10248
val=4,10249
val=5,10250
val=6,10251
val=7,10244
val=8,10252
val=9,10253
-----
[3970]
inputtype=int
outputtype=string
default=
val=null,Monster
val=0,All
val=1,Abyssal Sire
val=2,Alchemical Hydra
val=3,Barrows
val=4,Bryophyta
val=5,Callisto
val=6,Cerberus
val=7,Chaos Elemental
val=8,Chaos Fanatic
val=9,Crazy Archaeologist
val=10,Chambers of Xeric
val=11,CM: Chambers of Xeric
val=12,Corporeal Beast
val=13,Commander Zilyana
val=14,DK: Prime
val=15,DK: Rex
val=16,DK: Supreme
val=17,Deranged Archaeologist
val=18,Crystalline Hunllef
val=19,Corrupted Hunllef
val=20,General Graardor
val=21,Giant Mole
val=22,Grotesque Guardians
val=23,Hespori
val=24,Kalphite Queen
val=25,King Black Dragon
val=26,Kraken
val=27,Kree'arra
val=28,K'ril Tsutsaroth
val=29,Mimic
val=30,Nex
val=31,Nightmare, The
val=32,Phantom Muspah
val=33,Phosani's Nightmare
val=34,Obor
val=35,Sarachnis
val=36,Scorpia
val=37,Skotizo
val=38,Tempoross
val=39,EM: Theatre of Blood
val=40,Theatre of Blood
val=41,HM: Theatre of Blood
val=42,Thermy
val=43,EM: Tombs of Amascut
val=44,Tombs of Amascut
val=45,XM: Tombs of Amascut
val=46,TzHaar-Ket-Rak
val=47,TzKal-Zuk
val=48,TzTok-Jad
val=49,Venenatis
val=50,Vet'ion
val=51,Vorkath
val=52,Wintertodt
val=53,Zalcano
val=54,Zulrah
val=55,Fragment of Seren
val=56,Glough
val=57,Galvek
val=58,Other
-----
[4069]
inputtype=int
outputtype=int
default=1
val=0,0
-----
[3541]
inputtype=obj
outputtype=namedobj
default=null
val=salve_amulet_4081,salve_amulet(i)_25250
val=seers_ring_6731,seers_ring_(i)_25258
val=archers_ring_6733,archers_ring_(i)_25260
val=warrior_ring_6735,warrior_ring_(i)_25262
val=berserker_ring_6737,berserker_ring_(i)_25264
val=black_mask_(10)_8901,black_mask_(10)_(i)_25266
val=black_mask_(9)_8903,black_mask_(9)_(i)_25267
val=black_mask_(8)_8905,black_mask_(8)_(i)_25268
val=black_mask_(7)_8907,black_mask_(7)_(i)_25269
val=black_mask_(6)_8909,black_mask_(6)_(i)_25270
val=black_mask_(5)_8911,black_mask_(5)_(i)_25271
val=black_mask_(4)_8913,black_mask_(4)_(i)_25272
val=black_mask_(3)_8915,black_mask_(3)_(i)_25273
val=black_mask_(2)_8917,black_mask_(2)_(i)_25274
val=black_mask_(1)_8919,black_mask_(1)_(i)_25275
val=black_mask_8921,black_mask_(i)_25276
val=salve_amulet_(e)_10588,salve_amulet(ei)_25278
val=slayer_helmet_11864,slayer_helmet_(i)_25177
val=ring_of_the_gods_12601,ring_of_the_gods_(i)_25252
val=tyrannical_ring_12603,tyrannical_ring_(i)_25254
val=treasonous_ring_12605,treasonous_ring_(i)_25256
val=ring_of_suffering_19550,ring_of_suffering_(i)_25246
val=black_slayer_helmet_19639,black_slayer_helmet_(i)_25179
val=green_slayer_helmet_19643,green_slayer_helmet_(i)_25181
val=red_slayer_helmet_19647,red_slayer_helmet_(i)_25183
val=ring_of_suffering_(r)_20655,ring_of_suffering_(ri)_25248
val=purple_slayer_helmet_21264,purple_slayer_helmet_(i)_25185
val=granite_ring_21739,granite_ring_(i)_25193
val=turquoise_slayer_helmet_21888,turquoise_slayer_helmet_(i)_25187
val=hydra_slayer_helmet_23073,hydra_slayer_helmet_(i)_25189
val=twisted_slayer_helmet_24370,twisted_slayer_helmet_(i)_25191
val=tztok_slayer_helmet_25898,tztok_slayer_helmet_(i)_25902
val=vampyric_slayer_helmet_25904,vampyric_slayer_helmet_(i)_25908
val=tzkal_slayer_helmet_25910,tzkal_slayer_helmet_(i)_25914
-----
[2132]
inputtype=int
outputtype=namedobj
default=null
val=0,infernal_cape_21295
-----
[3978]
inputtype=int
outputtype=int
default=null
val=1,0
val=2,1
val=3,2
val=4,3
val=5,4
val=6,5
val=7,6
val=8,7
val=9,10
val=12,9
val=13,8
val=14,11
val=15,11
val=16,11
val=18,13
val=19,13
val=20,14
val=21,15
val=22,16
val=23,17
val=24,19
val=25,20
val=26,21
val=27,22
val=28,23
val=29,5
val=30,24
val=31,25
val=32,27
val=33,25
val=34,26
val=35,28
val=36,29
val=37,30
val=38,31
val=42,32
val=46,18
val=47,18
val=48,12
val=49,33
val=50,34
val=51,35
val=52,36
val=53,37
val=54,38
-----
[1695]
inputtype=int
outputtype=int
default=1
val=0,0
val=1,0
-----
[2124]
inputtype=int
outputtype=namedobj
default=null
val=0,kbd_heads_7980
val=1,dragon_pickaxe_11920
val=2,draconic_visage_11286
-----
[3549]
inputtype=int
outputtype=namedobj
default=null
val=0,gnome_child_mask_25336
val=1,gnome_child_icon_25338
-----
[10012]
inputtype=int
outputtype=string
default=
val=0,Easy - 80x XP, No drop rate bonus, 1x Exiles Points
val=1,Medium - 40x XP, Slightly better drop rate, 1.3x Exiles Points
val=2,Hard - 20x XP, Better drop rate, 1.6x Exiles Points
val=3,Extreme - 5x XP, Significantly better drop rate, 2x Exiles Points
-----
[2620]
inputtype=int
outputtype=enum
default=null
val=0,2622
val=1,2838
val=2,3028
val=3,3029
val=4,3030
-----
[2521]
inputtype=int
outputtype=component
default=null
val=0,707:25
val=1,707:26
val=2,707:27
val=3,707:28
val=4,707:29
val=5,707:30
val=6,707:31
-----
[2092]
inputtype=int
outputtype=string
default=No Progress
val=0,In Progress
val=1,Not Started
val=2,Completed
-----
[4863]
inputtype=int
outputtype=namedobj
default=null
val=0,fish_sack_barrel_25585
val=1,open_fish_sack_barrel_25587
-----
[10004]
inputtype=int
outputtype=int
default=null
val=0,10098
val=1,10099
val=2,10100
val=3,10101
val=4,10102
val=5,10103
val=6,10104
val=7,10105
val=8,10106
val=9,10107
val=10,10108
val=11,10109
val=12,10110
val=13,10111
val=14,10112
-----
[683]
inputtype=int
outputtype=component
default=null
val=0,162:5
val=1,162:8
val=2,162:12
val=3,162:16
val=4,162:20
val=5,162:24
val=6,162:28
-----
[2529]
inputtype=int
outputtype=namedobj
default=null
val=0,trout_334
val=1,leather_boots_1061
val=2,leather_gloves_1059
-----
[4375]
inputtype=int
outputtype=struct
default=null
val=1,4323
val=2,4322
val=3,4321
val=4,4320
val=5,4319
val=6,4318
val=7,4317
val=8,4316
val=9,4315
val=10,4314
val=11,4313
-----
[3938]
inputtype=int
outputtype=namedobj
default=null
val=0,little_nightmare_24491
val=1,little_parasite_25836
-----
[3017]
inputtype=namedobj
outputtype=int
default=1
val=iron_arrow_884,100
val=studded_chaps_1097,1
-----
[2100]
inputtype=int
outputtype=string
default=Shift
val=0,None
val=1,Shift
val=2,Ctrl
-----
[3946]
inputtype=int
outputtype=string
default=
val=0,all
val=1,easy
val=2,medium
val=3,hard
val=4,elite
val=5,master
val=6,grandmaster
-----
[1064]
inputtype=int
outputtype=namedobj
default=null
val=null,adamant_trimmed_set_(sk)_13018
val=0,adamant_full_helm_(t)_2605
val=1,adamant_platebody_(t)_2599
val=2,adamant_plateskirt_(t)_3474
val=3,adamant_kiteshield_(t)_2603
-----
[2910]
inputtype=int
outputtype=string
default=
val=0,Protect Item prayer enabled
val=1,PK Skull active
val=2,Killed by a player
val=3,Wilderness beyond level 20
-----
[3307]
inputtype=int
outputtype=namedobj
default=null
val=0,fire_max_hood_13330
val=1,saradomin_max_hood_13332
val=2,zamorak_max_hood_13334
val=3,guthix_max_hood_13336
val=4,imbued_saradomin_max_hood_21778
val=5,imbued_zamorak_max_hood_21782
val=6,imbued_guthix_max_hood_21786
val=7,accumulator_max_hood_13338
val=8,ardougne_max_hood_20764
val=9,infernal_max_hood_21282
val=10,assembler_max_hood_21900
val=11,mythical_max_hood_24857
val=12,masori_assembler_max_hood_27366
-----
[1080]
inputtype=int
outputtype=namedobj
default=null
val=null,guthix_armour_set_(sk)_13050
val=0,guthix_full_helm_2673
val=1,guthix_platebody_2669
val=2,guthix_plateskirt_3480
val=3,guthix_kiteshield_2675
-----
[3291]
inputtype=int
outputtype=namedobj
default=null
val=0,mime_mask_3057
val=1,frog_mask_6188
val=2,zombie_shirt_7592
val=3,camo_top_6654
val=4,lederhosen_top_6180
val=5,shade_robe_top_546
val=6,stale_baguette_20590
val=7,beekeeper's_hat_25129
-----
[10052]
inputtype=int
outputtype=int
default=null
val=0,13280
val=1,19476
val=2,32261
val=3,25936
-----
[10036]
inputtype=int
outputtype=int
default=null
val=0,10037
val=1,10038
val=2,10038
val=3,10038
val=4,10039
val=5,10039
-----
[10020]
inputtype=int
outputtype=int
default=null
val=0,111869978
val=1,111869979
val=2,111869980
val=3,111869981
val=4,111869982
val=5,111869983
val=6,111869984
-----
[4343]
inputtype=int
outputtype=location
default=null
val=0,null_14123
val=1,null_14122
val=2,null_14123
val=3,null_14122
val=4,null_14121
val=5,null_14122
val=6,null_14123
val=7,null_14122
val=8,null_14123
-----
[3057]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=1_1_0,wooden_table_24890
val=1_2_0,oak_table_24891
val=1_3_0,teak_table_24892
val=1_4_0,mahogany_table_24893
val=2_1_0,null_8245
val=3_1_0,wooden_shelves_24914
val=3_2_0,oak_shelves_24915
val=3_3_0,teak_shelves_24916
val=3_4_0,mahogany_shelves_24917
val=4_1_0,wooden_cupboard_24934
val=4_2_0,oak_cupboard_24935
val=4_3_0,teak_cupboard_24936
val=4_4_0,mahogany_cupboard_24937
val=5_1_0,wooden_bed_24918
val=5_2_0,oak_bed_24919
val=5_3_0,teak_bed_24920
val=5_4_0,mahogany_bed_24921
val=6_1_0,wooden_table_24886
val=6_2_0,oak_table_24887
val=6_3_0,teak_table_24888
val=6_4_0,mahogany_table_24889
val=7_1_0,wooden_table_24886
val=7_2_0,oak_table_24887
val=7_3_0,teak_table_24888
val=7_4_0,mahogany_table_24889
val=8_1_0,null_8245
-----
[814]
inputtype=int
outputtype=namedobj
default=null
val=0,super_ranging_(1)_11725
val=1,super_magic_potion_(1)_11729
val=2,overload_(1)_11733
val=3,absorption_(1)_11737
-----
[1874]
inputtype=int
outputtype=namedobj
default=null
val=0,_3952
val=1,_3954
val=2,_3956
val=3,_3958
val=4,_3960
val=5,_3962
val=6,_3964
val=7,_3966
val=8,_3968
val=9,_3970
val=10,_3972
val=11,_3974
val=12,_3976
val=13,_3978
val=14,_3980
val=15,_3982
val=16,_3984
val=17,_3986
val=18,_3988
val=19,_3990
val=20,_3992
val=21,_3994
val=22,_3996
val=23,_3998
-----
[2628]
inputtype=int
outputtype=namedobj
default=null
val=1,arceuus_library_teleport_19613
val=2,draynor_manor_teleport_19615
val=3,battlefront_teleport_22949
val=4,mind_altar_teleport_19617
val=5,salve_graveyard_teleport_19619
val=6,fenkenstrain's_castle_teleport_19621
val=7,west_ardougne_teleport_19623
val=8,harmony_island_teleport_19625
val=9,cemetery_teleport_19627
val=10,barrows_teleport_19629
val=11,ape_atoll_teleport_19631
-----
[1461]
inputtype=int
outputtype=component
default=null
val=1,458:4
val=2,458:5
val=3,458:6
val=4,458:7
val=5,458:8
val=6,458:9
val=7,458:10
val=8,458:11
val=9,458:12
val=10,458:13
val=11,458:14
val=12,458:15
val=13,458:16
val=14,458:17
val=15,458:18
val=16,458:19
val=17,458:20
val=18,458:21
val=19,458:22
val=20,458:23
val=21,458:24
val=22,458:25
val=23,458:26
val=24,458:27
val=25,458:28
val=26,458:29
val=27,458:30
val=28,458:31
val=29,458:32
val=30,458:33
val=31,458:34
-----
[4327]
inputtype=int
outputtype=location
default=null
val=0,null_14069
val=1,null_14068
val=2,null_14069
val=3,null_14068
val=4,null_14067
val=5,null_14068
val=6,null_14069
val=7,null_14068
val=8,null_14069
-----
[1048]
inputtype=int
outputtype=namedobj
default=null
val=null,steel_set_(sk)_12986
val=0,steel_full_helm_1157
val=1,steel_platebody_1119
val=2,steel_plateskirt_1083
val=3,steel_kiteshield_1193
-----
[2644]
inputtype=int
outputtype=component
default=665:19
val=0,665:19
val=1,665:20
val=2,665:21
val=3,665:22
val=4,665:23
val=5,665:26
val=6,665:27
val=7,665:28
val=8,665:29
val=9,665:30
-----
[4077]
inputtype=location
outputtype=int
default=0
val=broken_fence_544,57
val=rocks_2231,15
val=rocks_3748,15
val=rocks_3803,15
val=rocks_3804,15
val=log_balance_3931,45
val=log_balance_3932,45
val=log_balance_3933,45
val=crevice_9739,67
val=crevice_9740,67
val=dark_tunnel_10047,54
val=stepping_stone_10663,76
val=stepping_stone_11768,55
val=stepping_stone_13504,50
val=stepping_stone_14918,74
val=rock_16115,25
val=rocks_16464,73
val=crevice_16465,86
val=stepping_stone_16466,77
val=tight-gap_16468,14
val=obstacle_pipe_16509,70
val=strange_floor_16510,80
val=obstacle_pipe_16511,51
val=stepping_stone_16513,66
val=fence_16518,13
val=castle_wall_16519,16
val=hole_16520,16
val=rocks_16521,41
val=rocks_16522,43
val=rocks_16523,44
val=rocks_16524,47
val=weathered_wall_16525,58
val=weathered_wall_16526,58
val=underwall_tunnel_16527,26
val=underwall_tunnel_16528,26
val=underwall_tunnel_16529,21
val=underwall_tunnel_16530,21
val=stepping_stone_16533,31
val=rocks_16534,37
val=rocks_16535,37
val=crevice_16539,62
val=log_balance_16540,48
val=log_balance_16542,48
val=crevice_16543,42
val=strange_floor_16544,81
val=log_balance_16546,33
val=log_balance_16548,33
val=rocks_16549,38
val=rocks_16550,38
val=ornate_railing_16552,65
val=rocks_16998,65
val=rocks_16999,65
val=ornate_railing_17000,65
val=rocks_17042,32
val=rocks_17043,32
val=wall_17047,39
val=wall_17048,4
val=wall_17049,11
val=wall_17050,11
val=wall_17051,4
val=wall_17052,4
val=crossbow_tree_17062,36
val=crossbow_tree_17063,36
val=broken_raft_17068,8
val=strong_tree_17074,53
val=underwall_tunnel_19032,42
val=underwall_tunnel_19036,42
val=stepping_stone_19040,83
val=stepping_stone_19042,60
val=crevice_19043,46
val=rocks_19849,25
val=stepping_stone_21738,12
val=stepping_stone_21739,12
val=obstacle_pipe_23140,49
val=log_balance_23274,20
val=pile_of_rubble_23563,67
val=pile_of_rubble_23564,67
val=ropeswing_23568,10
val=ropeswing_23569,10
val=crumbling_wall_24222,5
val=door_25526,23
val=door_25527,23
val=little_crack_26382,60
val=rocky_handholds_26405,60
val=null_26561,70
val=null_26562,70
val=vine_26880,87
val=vine_26882,87
val=null_26884,79
val=null_26886,79
val=rocks_27984,73
val=rocks_27985,73
val=rocks_27987,52
val=rocks_27988,52
val=boulder_27990,49
val=loose_railing_28849,63
val=stone_28893,28
val=gap_29326,60
val=stepping_stone_29728,45
val=stepping_stone_29729,40
val=stepping_stone_29730,40
val=tunnel_30174,72
val=tunnel_30175,72
val=rope_anchor_30916,64
val=rope_anchor_30917,64
val=hole_31481,70
val=hole_31482,70
val=rocks_31757,10
val=rocks_31758,30
val=rocks_31759,30
val=pillar_31809,15
val=null_31849,23
val=null_31852,23
val=broken_wall_33344,70
val=big_window_33348,70
val=rocks_34396,62
val=rocks_34397,29
val=mysterious_pipe_34655,88
val=rocks_34741,69
val=strange_floor_34834,75
val=tight_gap_36692,78
val=tight_gap_36693,78
val=tight_gap_36694,84
val=tight_gap_36695,84
val=null_39541,63
val=null_39542,63
val=crumbling_wall_40355,71
val=rocks_40356,71
val=null_40960,91
val=null_40962,91
val=tunnel_42506,72
val=tunnel_42507,72
val=rubble_43724,56
val=rubble_43726,56
val=stepping_stone_43989,62
val=stepping_stone_43990,62
-----
[425]
inputtype=int
outputtype=struct
default=null
val=0,3623
val=1,2754
val=2,2755
val=3,2753
val=4,2974
val=5,2756
-----
[4101]
inputtype=int
outputtype=namedobj
default=null
val=0,shattered_hood_(t1)_26427
val=1,shattered_top_(t1)_26430
val=2,shattered_trousers_(t1)_26433
val=3,shattered_boots_(t1)_26436
-----
[28]
inputtype=int
outputtype=namedobj
default=null
val=0,villager_hat_6355
val=1,tribal_top_6351
val=2,villager_robe_6353
val=3,villager_armband_6359
val=4,villager_sandals_6357
-----
[1866]
inputtype=obj
outputtype=int
default=null
val=black-green_ankou_set_2749,0
val=black-purple_ankou_set_2750,1
val=black-white_ankou_set_2751,2
val=black-yellow_ankou_set_2752,3
val=gilded_ankou_set_2753,4
val=green-black_ankou_set_2754,5
val=purple-black_ankou_set_2755,6
val=red-black_ankou_set_2756,7
val=white-blue_ankou_set_2757,8
val=white-purple_ankou_set_2758,9
val=_2759,10
val=_2760,11
val=_2761,12
val=_2762,13
val=_2763,14
val=_2764,15
val=_2765,16
val=_2766,17
val=_2767,18
val=_2768,19
val=_2769,20
val=_2770,21
val=_2771,22
val=_2772,23
-----
[4093]
inputtype=struct
outputtype=int
default=0
val=3335,1
val=3399,1
val=3435,1
val=3464,1
-----
[1445]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=1_1_0,oak_lectern_8334
val=1_2_0,eagle_lectern_8335
val=1_3_0,demon_lectern_8336
val=1_4_0,teak_eagle_lectern_8337
val=1_5_0,teak_demon_lectern_8338
val=1_6_0,mahogany_eagle_8339
val=1_7_0,mahogany_demon_8340
val=1_8_0,marble_lectern_24132
val=2_1_0,globe_8341
val=2_2_0,ornamental_globe_8342
val=2_3_0,lunar_globe_8343
val=2_4_0,celestial_globe_8344
val=2_5_0,armillary_sphere_8345
val=2_6_0,small_orrery_8346
val=2_7_0,large_orrery_8347
val=4_1_0,crystal_ball_8351
val=4_2_0,elemental_sphere_8352
val=4_3_0,crystal_of_power_8353
val=5_1_0,alchemical_chart_8354
val=5_2_0,astronomical_chart_8355
val=5_3_0,infernal_chart_8356
val=5_4_0,s.t.a.s.h_chart_25611
val=6_1_0,oak_telescope_8348
val=6_2_0,teak_telescope_8349
val=6_3_0,mahogany_telescope_8350
val=7_1_0,wooden_bookcase_8319
val=7_2_0,oak_bookcase_8320
val=7_3_0,mahogany_bookcase_8321
-----
[3267]
inputtype=int
outputtype=namedobj
default=null
val=0,ankou_mask_20095
val=1,ankou_top_20098
val=2,ankou's_leggings_20104
val=3,ankou_gloves_20101
val=4,ankou_socks_20107
-----
[3283]
inputtype=int
outputtype=namedobj
default=null
val=0,eggshell_platebody_22351
val=1,eggshell_platelegs_22353
-----
[1437]
inputtype=coordgrid
outputtype=obj
default=null_8245
val=2_1_0,jester_8159
val=2_2_0,treasure_hunt_8160
val=2_3_0,hangman_game_8161
val=4_1_0,oak_prize_chest_8165
val=4_2_0,teak_prize_chest_8166
val=4_3_0,mahogany_chest_8167
val=5_1_0,clay_attack_stone_8153
val=5_2_0,attack_stone_8154
val=5_3_0,marble_att._stone_8155
val=6_1_0,magical_balance_1_8156
val=6_2_0,magical_balance_2_8157
val=6_3_0,magical_balance_3_8158
val=7_1_0,hoop_and_stick_8162
val=7_2_0,dartboard_8163
val=7_3_0,archery_target_8164
-----
[4109]
inputtype=int
outputtype=struct
default=null
val=0,4096
val=1,4093
val=2,4094
val=3,4095
val=4,4090
val=5,4091
val=6,4092
val=7,4097
val=8,4098
-----
[1429]
inputtype=int
outputtype=coordgrid
default=null
val=1,5_1_0
val=2,5_2_0
val=3,5_3_0
val=4,5_4_0
val=5,5_5_0
val=6,5_6_0
val=7,5_7_0
val=8,5_8_0
val=9,5_9_0
val=10,5_10_0
val=11,5_11_0
val=12,5_12_0
val=13,5_13_0
val=14,5_14_0
val=15,5_15_0
val=16,5_16_0
val=17,5_17_0
val=18,5_18_0
val=19,5_19_0
val=20,5_20_0
val=21,5_21_0
val=22,5_22_0
val=23,5_23_0
val=24,5_24_0
val=25,5_25_0
val=26,5_26_0
val=27,5_27_0
val=28,5_28_0
val=29,5_29_0
val=30,5_30_0
val=31,5_31_0
-----
[4117]
inputtype=int
outputtype=string
default=null
val=0,Leagues are time-limited game modes in which you can experience Old School RuneScape like never before!|Complete tasks to earn League Points that can be used to unlock powerful Fragments which enhance your gameplay!|Each League is different - figure out the best strategy to pull ahead of the competition. Or alternatively, just have fun!
val=1,In Leagues, League Points can be earned by completing various tasks. They can then be used to unlock new Fragment Slots.|League Points are also transferred to your main game profile where they can be used to buy rewards in the Leagues Reward Shop.
val=2,Fragments are useful buffs. You can mix and match different Fragments to create interesting combinations.|Each Fragment has two Set Effects. When you equip enough Fragments with the same Set Effect, it'll get more powerful!|Completing tasks also earns you Sage's Renown which can be used to unlock content like Skills and Bosses.
val=3,Your performance in the League is measured by how many League Points you earn. And the more you score compared to your rivals, the higher League Rank you'll achieve!|The cut-offs for League Ranks are:|- Bronze: Earn 100 League Points<br>- Iron: Top 80%<br>- Steel: Top 60%<br>- Mithril: Top 40%<br>- Adamant: Top 20%<br>- Rune: Top 5%<br>- Dragon: Top 1%
val=4,As well as letting you unlock Fragments, League Points are transferred to your main game profile.|Points can be used to buy rewards in the Leagues Reward Shop.|Performance in one League will also contribute to the next, with rewards such as:|- League Starter Outfits<br>- League Icons<br>- League Participation Worlds<br>- Pet Transfers
-----
[3275]
inputtype=int
outputtype=namedobj
default=null
val=0,cow_mask_11919
val=1,cow_top_12956
val=2,cow_trousers_12957
val=3,cow_gloves_12958
val=4,cow_shoes_12959
-----
[2144]
inputtype=int
outputtype=namedobj
default=null
val=0,ring_of_3rd_age_23185
val=1,fury_ornament_kit_12526
val=2,dragon_chainbody_ornament_kit_12534
val=3,dragon_legs/skirt_ornament_kit_12536
val=4,dragon_sq_shield_ornament_kit_12532
val=5,dragon_full_helm_ornament_kit_12538
val=6,dragon_scimitar_ornament_kit_20002
val=7,light_infinity_colour_kit_12530
val=8,dark_infinity_colour_kit_12528
val=9,holy_wraps_19997
val=10,ranger_gloves_19994
val=11,rangers'_tunic_12596
val=12,rangers'_tights_23249
val=13,black_d'hide_body_(g)_12381
val=14,black_d'hide_chaps_(g)_12383
val=15,black_d'hide_body_(t)_12385
val=16,black_d'hide_chaps_(t)_12387
val=17,royal_crown_12397
val=18,royal_sceptre_12439
val=19,royal_gown_top_12393
val=20,royal_gown_bottom_12395
val=21,musketeer_hat_12351
val=22,musketeer_tabard_12441
val=23,musketeer_pants_12443
val=24,dark_tuxedo_jacket_19958
val=25,dark_trousers_19964
val=26,dark_tuxedo_shoes_19967
val=27,dark_tuxedo_cuffs_19961
val=28,dark_bow_tie_19970
val=29,light_tuxedo_jacket_19973
val=30,light_trousers_19979
val=31,light_tuxedo_shoes_19982
val=32,light_tuxedo_cuffs_19976
val=33,light_bow_tie_19985
val=34,arceuus_scarf_19943
val=35,hosidius_scarf_19946
val=36,piscarilius_scarf_19952
val=37,shayzien_scarf_19955
val=38,lovakengj_scarf_19949
val=39,bronze_dragon_mask_12363
val=40,iron_dragon_mask_12365
val=41,steel_dragon_mask_12367
val=42,mithril_dragon_mask_12369
val=43,adamant_dragon_mask_23270
val=44,rune_dragon_mask_23273
val=45,katana_12357
val=46,dragon_cane_12373
val=47,briefcase_12335
val=48,bucket_helm_19991
val=49,blacksmith's_helm_19988
val=50,deerstalker_12540
val=51,afro_12430
val=52,big_pirate_hat_12355
val=53,top_hat_12432
val=54,monocle_12353
val=55,sagacious_spectacles_12337
val=56,fremennik_kilt_23246
val=57,giant_boot_23252
val=58,uri's_hat_23255
-----
[4188]
inputtype=int
outputtype=namedobj
default=null
val=null,shattered_relic_hunter_(t1)_armour_set_26554
val=0,shattered_boots_(t1)_26436
val=1,shattered_hood_(t1)_26427
val=2,shattered_trousers_(t1)_26433
val=3,shattered_top_(t1)_26430
-----
[3200]
inputtype=int
outputtype=namedobj
default=null
val=0,blue_wizard_hat_(t)_7396
val=1,blue_wizard_robe_(t)_7392
val=2,blue_skirt_(t)_7388
-----
[3132]
inputtype=int
outputtype=namedobj
default=null
val=0,desert_camo_top_10061
val=1,desert_camo_legs_10063
-----
[2541]
inputtype=int
outputtype=namedobj
default=null
val=0,salmon_330
val=1,strength_potion(4)_114
-----
[3926]
inputtype=int
outputtype=struct
default=3721
val=0,3722
val=1,3723
val=2,3723
val=3,3725
val=4,3721
val=5,3721
-----
[4387]
inputtype=int
outputtype=component
default=null
val=0,756:20
val=1,756:21
val=2,756:22
val=3,756:23
val=4,756:24
val=5,756:25
val=7,756:26
val=9,756:27
val=10,756:28
val=12,756:29
val=13,756:30
-----
[2735]
inputtype=int
outputtype=struct
default=null
val=1,724
val=2,725
val=3,726
-----
[1548]
inputtype=int
outputtype=struct
default=758
val=0,758
val=1,759
val=2,760
val=3,761
-----
[3136]
inputtype=int
outputtype=namedobj
default=null
val=0,ham_hood_4302
val=1,ham_shirt_4298
val=2,ham_robe_4300
val=3,ham_logo_4306
val=4,ham_cloak_4304
val=5,ham_gloves_4308
val=6,ham_boots_4310
-----
[4649]
inputtype=int
outputtype=namedobj
default=null
val=0,masori_mask_(f)_27235
val=1,masori_body_(f)_27238
val=2,masori_chaps_(f)_27241
-----
[2739]
inputtype=int
outputtype=struct
default=null
val=1,736
val=2,737
val=3,738
-----
[3994]
inputtype=int
outputtype=string
default=Depends on combat levels
val=0,Depends on combat levels
val=1,Always long-tap
val=2,Tap where available
val=3,Hidden
val=4,Long-tap for clanmates
-----
[4323]
inputtype=int
outputtype=location
default=null
val=0,null_30482
val=1,null_31300
val=2,null_30480
val=3,null_31301
val=4,null_30481
val=5,null_31302
-----
[1612]
inputtype=int
outputtype=namedobj
default=null
val=null,dragon_armour_set_(sk)_21885
val=0,dragon_full_helm_11335
val=1,dragon_platebody_21892
val=2,dragon_plateskirt_4585
val=3,dragon_kiteshield_21895
-----
[3335]
inputtype=component
outputtype=graphic
default=null
val=512:68,2767
val=512:70,2768
val=512:71,2770
val=512:72,2771
val=512:73,2769
val=512:74,2773
val=512:75,2774
val=512:76,2775
val=512:77,2772
-----
[2997]
inputtype=namedobj
outputtype=int
default=1
val=amulet_of_magic_1727,1
-----
[3930]
inputtype=int
outputtype=struct
default=3721
val=0,3722
val=1,3723
val=2,3721
val=3,3725
val=4,3728
val=5,3725
-----
[2545]
inputtype=int
outputtype=enum
default=null
val=0,2547
val=1,2549
-----
[4391]
inputtype=obj
outputtype=boolean
default=0
val=strength_potion(4)_113,1
val=mithril_seeds_299,1
val=swordfish_373,1
val=shark_385,1
val=manta_ray_391,1
val=monk's_robe_542,1
val=monk's_robe_top_544,1
val=rune_thrownaxe_805,1
val=adamant_dart_810,1
val=rune_dart_811,1
val=adamant_javelin_829,1
val=rune_javelin_830,1
val=maple_shortbow_853,1
val=magic_shortbow_861,1
val=rune_knife_868,1
val=adamant_arrow_890,1
val=rune_arrow_892,1
val=green_d'hide_vambraces_1065,1
val=rune_platelegs_1079,1
val=rune_plateskirt_1093,1
val=green_d'hide_chaps_1099,1
val=rune_platebody_1127,1
val=leather_body_1129,1
val=green_d'hide_body_1135,1
val=rune_full_helm_1163,1
val=coif_1169,1
val=dragon_sq_shield_1187,1
val=rune_kiteshield_1201,1
val=dragon_dagger_1215,1
val=dragon_spear_1249,1
val=dragon_longsword_1305,1
val=rune_2h_sword_1319,1
val=rune_scimitar_1333,1
val=dragon_mace_1434,1
val=amulet_of_strength_1725,1
val=amulet_of_power_1731,1
val=anchovy_pizza_2297,1
val=pineapple_pizza_2301,1
val=saradomin_cape_2412,1
val=guthix_cape_2413,1
val=zamorak_cape_2414,1
val=saradomin_staff_2415,1
val=guthix_staff_2416,1
val=zamorak_staff_2417,1
val=prayer_potion(4)_2434,1
val=super_attack(4)_2436,1
val=super_strength(4)_2440,1
val=super_defence(4)_2442,1
val=ranging_potion(4)_2444,1
val=superantipoison(4)_2448,1
val=blue_d'hide_vambraces_2487,1
val=red_d'hide_vambraces_2489,1
val=black_d'hide_vambraces_2491,1
val=blue_d'hide_chaps_2493,1
val=red_d'hide_chaps_2495,1
val=black_d'hide_chaps_2497,1
val=blue_d'hide_body_2499,1
val=red_d'hide_body_2501,1
val=black_d'hide_body_2503,1
val=ring_of_recoil_2550,1
val=ranger_boots_2577,1
val=wizard_boots_2579,1
val=robin_hood_hat_2581,1
val=super_restore(4)_3024,1
val=magic_potion(4)_3040,1
val=climbing_boots_3105,1
val=dragon_chainbody_3140,1
val=cooked_karambwan_3144,1
val=archer_helm_3749,1
val=berserker_helm_3751,1
val=warrior_helm_3753,1
val=farseer_helm_3755,1
val=holy_book_3840,1
val=unholy_book_3842,1
val=book_of_balance_3844,1
val=mystic_hat_4089,1
val=mystic_robe_top_4091,1
val=mystic_robe_bottom_4093,1
val=rune_boots_4131,1
val=abyssal_whip_4151,1
val=granite_maul_4153,1
val=leaf-bladed_spear_4158,1
val=slayer's_staff_4170,1
val=guthix_rest(4)_4417,1
val=dragon_scimitar_4587,1
val=ancient_staff_4675,1
val=ahrim's_hood_4708,1
val=ahrim's_staff_4710,1
val=ahrim's_robetop_4712,1
val=ahrim's_robeskirt_4714,1
val=dharok's_helm_4716,1
val=dharok's_greataxe_4718,1
val=dharok's_platebody_4720,1
val=dharok's_platelegs_4722,1
val=guthan's_helm_4724,1
val=guthan's_warspear_4726,1
val=guthan's_platebody_4728,1
val=guthan's_chainskirt_4730,1
val=karil's_coif_4732,1
val=karil's_crossbow_4734,1
val=karil's_leathertop_4736,1
val=karil's_leatherskirt_4738,1
val=bolt_rack_4740,1
val=torag's_helm_4745,1
val=torag's_hammers_4747,1
val=torag's_platebody_4749,1
val=torag's_platelegs_4751,1
val=verac's_helm_4753,1
val=verac's_flail_4755,1
val=verac's_brassard_4757,1
val=verac's_plateskirt_4759,1
val=initiate_sallet_5574,1
val=initiate_hauberk_5575,1
val=initiate_cuisse_5576,1
val=rune_knife(p++)_5667,1
val=dragon_dagger(p++)_5698,1
val=ghostly_robe_6107,1
val=ghostly_robe_6108,1
val=ghostly_hood_6109,1
val=snakeskin_body_6322,1
val=snakeskin_chaps_6324,1
val=snakeskin_bandana_6326,1
val=snakeskin_boots_6328,1
val=snakeskin_vambraces_6330,1
val=toktz-xil-ul_6522,1
val=toktz-xil-ak_6523,1
val=toktz-ket-xil_6524,1
val=toktz-xil-ek_6525,1
val=toktz-mej-tal_6526,1
val=tzhaar-ket-em_6527,1
val=tzhaar-ket-om_6528,1
val=fire_cape_6570,1
val=amulet_of_fury_6585,1
val=saradomin_brew(4)_6685,1
val=mage's_book_6889,1
val=master_wand_6914,1
val=infinity_top_6916,1
val=infinity_hat_6918,1
val=infinity_boots_6920,1
val=infinity_gloves_6922,1
val=infinity_bottoms_6924,1
val=dragon_2h_sword_7158,1
val=enchanted_robe_7398,1
val=enchanted_top_7399,1
val=enchanted_hat_7400,1
val=mithril_gloves_7458,1
val=adamant_gloves_7459,1
val=rune_gloves_7460,1
val=barrows_gloves_7462,1
val=void_knight_top_8839,1
val=void_knight_robe_8840,1
val=void_knight_mace_8841,1
val=void_knight_gloves_8842,1
val=mithril_defender_8848,1
val=adamant_defender_8849,1
val=rune_defender_8850,1
val=bone_dagger_(p++)_8878,1
val=mithril_bolts_9142,1
val=adamant_bolts_9143,1
val=runite_bolts_9144,1
val=rune_crossbow_9185,1
val=diamond_bolts_(e)_9243,1
val=dragonstone_bolts_(e)_9244,1
val=onyx_bolts_(e)_9245,1
val=proselyte_sallet_9672,1
val=proselyte_hauberk_9674,1
val=proselyte_cuisse_9676,1
val=proselyte_tasset_9678,1
val=chinchompa_10033,1
val=red_chinchompa_10034,1
val=saradomin_mitre_10452,1
val=guthix_mitre_10454,1
val=zamorak_mitre_10456,1
val=saradomin_robe_top_10458,1
val=zamorak_robe_top_10460,1
val=guthix_robe_top_10462,1
val=saradomin_robe_legs_10464,1
val=guthix_robe_legs_10466,1
val=zamorak_robe_legs_10468,1
val=ava's_accumulator_10499,1
val=fighter_hat_10548,1
val=fighter_torso_10551,1
val=helm_of_neitiznot_10828,1
val=barrelchest_anchor_10887,1
val=sanfew_serum(4)_10925,1
val=brine_sabre_11037,1
val=phoenix_necklace_11090,1
val=berserker_necklace_11128,1
val=regen_bracelet_11133,1
val=dwarven_helmet_11200,1
val=dragon_arrow_11212,1
val=dragon_dart_11230,1
val=dark_bow_11235,1
val=dragonfire_shield_11283,1
val=void_mage_helm_11663,1
val=void_ranger_helm_11664,1
val=void_melee_helm_11665,1
val=seers_ring_(i)_11770,1
val=archers_ring_(i)_11771,1
val=warrior_ring_(i)_11772,1
val=berserker_ring_(i)_11773,1
val=armadyl_crossbow_11785,1
val=staff_of_the_dead_11791,1
val=armadyl_godsword_11802,1
val=bandos_godsword_11804,1
val=saradomin_godsword_11806,1
val=zamorak_godsword_11808,1
val=zamorakian_spear_11824,1
val=armadyl_helmet_11826,1
val=armadyl_chestplate_11828,1
val=armadyl_chainskirt_11830,1
val=bandos_chestplate_11832,1
val=bandos_tassets_11834,1
val=bandos_boots_11836,1
val=saradomin_sword_11838,1
val=dragon_boots_11840,1
val=graceful_hood_11850,1
val=graceful_cape_11852,1
val=graceful_top_11854,1
val=graceful_legs_11856,1
val=graceful_gloves_11858,1
val=graceful_boots_11860,1
val=slayer_helmet_(i)_11865,1
val=zamorakian_hasta_11889,1
val=decorative_armour_11899,1
val=leaf-bladed_sword_11902,1
val=malediction_ward_11924,1
val=odium_ward_11926,1
val=dark_crab_11936,1
val=black_chinchompa_11959,1
val=amulet_of_glory(6)_11978,1
val=mystic_smoke_staff_12000,1
val=occult_necklace_12002,1
val=abyssal_tentacle_12006,1
val=salve_amulet(ei)_12018,1
val=ancient_bracers_12490,1
val=ancient_d'hide_body_12492,1
val=ancient_chaps_12494,1
val=ancient_coif_12496,1
val=rangers'_tunic_12596,1
val=book_of_war_12608,1
val=book_of_law_12610,1
val=book_of_darkness_12612,1
val=saradomin_halo_12637,1
val=zamorak_halo_12638,1
val=guthix_halo_12639,1
val=tyrannical_ring_(i)_12691,1
val=treasonous_ring_(i)_12692,1
val=super_combat_potion(4)_12695,1
val=magic_shortbow_(i)_12788,1
val=sara's_blessed_sword_(full)_12808,1
val=spirit_shield_12829,1
val=blessed_spirit_shield_12831,1
val=amulet_of_the_damned_(full)_12851,1
val=toxic_staff_of_the_dead_12904,1
val=anti-venom+(4)_12913,1
val=toxic_blowpipe_12926,1
val=zulrah's_scales_12934,1
val=dragon_defender_12954,1
val=elite_void_top_13072,1
val=elite_void_robe_13073,1
val=ardougne_cloak_4_13124,1
val=ring_of_the_gods_(i)_13202,1
val=abyssal_bludgeon_13263,1
val=abyssal_dagger_13265,1
val=xerician_hat_13385,1
val=xerician_top_13387,1
val=xerician_robe_13389,1
val=anglerfish_13441,1
val=dragon_warhammer_13576,1
val=dragon_claws_13652,1
val=light_ballista_19478,1
val=heavy_ballista_19481,1
val=dragon_javelin_19484,1
val=tormented_bracelet_19544,1
val=necklace_of_anguish_19547,1
val=amulet_of_torture_19553,1
val=elder_chaos_top_20517,1
val=elder_chaos_robe_20520,1
val=elder_chaos_hood_20595,1
val=tome_of_fire_20714,1
val=burnt_page_20718,1
val=imbued_heart_20724,1
val=leaf-bladed_battleaxe_20727,1
val=dragon_thrownaxe_20849,1
val=twisted_bow_20997,1
val=elder_maul_21003,1
val=kodai_wand_21006,1
val=dragon_sword_21009,1
val=dragon_hunter_crossbow_21012,1
val=dinh's_bulwark_21015,1
val=ancestral_hat_21018,1
val=ancestral_robe_top_21021,1
val=ancestral_robe_bottom_21024,1
val=infernal_cape_21295,1
val=obsidian_helmet_21298,1
val=obsidian_platebody_21301,1
val=obsidian_platelegs_21304,1
val=amethyst_javelin_21318,1
val=amethyst_arrow_21326,1
val=ancient_wyvern_shield_21633,1
val=granite_longsword_21646,1
val=guardian_boots_21733,1
val=granite_gloves_21736,1
val=granite_hammer_21742,1
val=granite_ring_(i)_21752,1
val=imbued_saradomin_cape_21791,1
val=imbued_guthix_cape_21793,1
val=imbued_zamorak_cape_21795,1
val=revenant_ether_21820,1
val=dragon_kiteshield_21895,1
val=dragon_crossbow_21902,1
val=dragon_bolts_21905,1
val=opal_dragon_bolts_(e)_21932,1
val=jade_dragon_bolts_(e)_21934,1
val=pearl_dragon_bolts_(e)_21936,1
val=topaz_dragon_bolts_(e)_21938,1
val=sapphire_dragon_bolts_(e)_21940,1
val=emerald_dragon_bolts_(e)_21942,1
val=ruby_dragon_bolts_(e)_21944,1
val=diamond_dragon_bolts_(e)_21946,1
val=dragonstone_dragon_bolts_(e)_21948,1
val=onyx_dragon_bolts_(e)_21950,1
val=dragonfire_ward_22002,1
val=locator_orb_22081,1
val=ava's_assembler_22109,1
val=dragonbone_necklace_22111,1
val=mythical_cape_22114,1
val=extended_super_antifire(4)_22209,1
val=staff_of_light_22296,1
val=avernic_defender_22322,1
val=sanguinesti_staff_22323,1
val=scythe_of_vitur_22325,1
val=justiciar_faceguard_22326,1
val=justiciar_chestguard_22327,1
val=justiciar_legguards_22328,1
val=bryophyta's_staff_22370,1
val=ivandis_flail_22398,1
val=battlemage_potion(4)_22449,1
val=bastion_potion(4)_22461,1
val=viggora's_chainmace_22545,1
val=craw's_bow_22550,1
val=thammaron's_sceptre_22555,1
val=dragon_hasta(p++)_22740,1
val=dragon_knife_22804,1
val=dragon_knife(p++)_22810,1
val=boots_of_brimstone_22951,1
val=devout_boots_22954,1
val=brimstone_ring_22975,1
val=dragon_hunter_lance_22978,1
val=spiked_manacles_23389,1
val=divine_super_combat_potion(4)_23685,1
val=divine_super_attack_potion(4)_23697,1
val=divine_super_strength_potion(4)_23709,1
val=divine_super_defence_potion(4)_23721,1
val=divine_ranging_potion(4)_23733,1
val=divine_magic_potion(4)_23745,1
val=crystal_helm_23971,1
val=crystal_body_23975,1
val=crystal_legs_23979,1
val=crystal_bow_24123,1
val=crystal_halberd_24125,1
val=crystal_shield_24127,1
val=granite_maul_24225,1
val=neitiznot_faceguard_24271,1
val=inquisitor's_mace_24417,1
val=inquisitor's_great_helm_24419,1
val=inquisitor's_hauberk_24420,1
val=inquisitor's_plateskirt_24421,1
val=nightmare_staff_24422,1
val=harmonised_nightmare_staff_24423,1
val=volatile_nightmare_staff_24424,1
val=eldritch_nightmare_staff_24425,1
val=divine_battlemage_potion(4)_24623,1
val=divine_bastion_potion(4)_24635,1
val=amulet_of_blood_fury_24780,1
val=tome_of_water_25574,1
val=soaked_page_25578,1
val=zaryte_crossbow_26374,1
val=saika's_hood_26731,1
val=saika's_veil_26733,1
val=saika's_shroud_26735,1
val=koriff's_headband_26737,1
val=koriff's_cowl_26739,1
val=koriff's_coif_26741,1
val=maoma's_med_helm_26743,1
val=maoma's_full_helm_26745,1
val=maoma's_great_helm_26747,1
val=calamity_chest_26749,1
val=superior_calamity_chest_26751,1
val=elite_calamity_chest_26753,1
val=calamity_breeches_26755,1
val=superior_calamity_breeches_26757,1
val=elite_calamity_breeches_26759,1
val=rune_pouch_27086,1
val=mithril_gloves_(wrapped)_27110,1
val=rune_gloves_(wrapped)_27111,1
val=barrows_gloves_(wrapped)_27112,1
-----
[2084]
inputtype=int
outputtype=namedobj
default=null
val=0,message_755
val=1,cadava_potion_756
-----
[2080]
inputtype=int
outputtype=namedobj
default=null
val=0,red_bead_1470
val=1,yellow_bead_1472
val=2,black_bead_1474
val=3,white_bead_1476
-----
[10000]
inputtype=int
outputtype=int
default=null
val=0,10001
val=1,10002
val=2,10003
val=3,10004
val=4,10005
val=5,10006
val=6,10007
val=7,10009
-----
[2005]
inputtype=int
outputtype=namedobj
default=null
val=0,star_amulet_4183
val=1,cavern_key_4184
val=2,tower_key_4185
val=3,shed_key_4186
val=4,marble_amulet_4187
val=5,obsidian_amulet_4188
val=6,garden_cane_4189
val=7,garden_brush_4190
val=8,extended_brush_4191
val=9,extended_brush_4192
val=10,extended_brush_4193
val=11,torso_4194
val=12,arms_4195
val=13,legs_4196
val=14,decapitated_head_4197
val=15,decapitated_head_4198
val=16,pickled_brain_4199
val=17,conductor_mould_4200
val=18,conductor_4201
val=19,journal_4203
val=20,letter_4204
-----
[2612]
inputtype=int
outputtype=obj
default=null
val=0,energy_potion(4)_3009
-----
[1687]
inputtype=int
outputtype=namedobj
default=null
val=0,pet_snakeling_12921
val=1,pet_snakeling_12939
val=2,pet_snakeling_12940
-----
[2009]
inputtype=int
outputtype=namedobj
default=null
val=0,square_stone_6119
val=1,square_stone_6120
val=2,letter_6121
-----
[2600]
inputtype=int
outputtype=obj
default=null
val=0,coins_995
-----
[2993]
inputtype=namedobj
outputtype=int
default=1
val=cosmic_rune_564,50
-----
[1163]
inputtype=int
outputtype=component
default=null
val=0,122:18
val=1,122:19
val=2,122:20
val=3,122:21
val=4,122:22
val=5,122:23
val=6,122:24
-----
[3009]
inputtype=int
outputtype=enum
default=null
val=0,3011
val=1,3013
-----
[3803]
inputtype=int
outputtype=component
default=null
val=0,700:21
val=1,700:22
val=2,700:23
val=3,700:24
val=4,700:25
val=5,700:26
val=6,700:27
val=7,700:28
val=8,700:29
val=9,700:30
val=10,700:31
val=11,700:32
val=12,700:33
val=13,700:34
val=14,700:35
val=15,700:36
val=16,700:37
val=17,700:38
val=18,700:39
val=19,700:40
val=20,700:41
val=21,700:42
val=22,700:43
val=23,700:44
val=24,700:45
val=25,700:46
val=26,700:47
val=27,700:48
val=28,700:49
val=29,700:50
val=30,700:51
val=31,700:52
val=32,700:53
val=33,700:54
val=34,700:55
val=35,700:56
val=36,700:57
val=37,700:58
val=38,700:59
val=39,700:60
val=40,700:61
val=41,700:62
val=42,700:63
val=43,700:64
val=44,700:65
-----
[1544]
inputtype=int
outputtype=int
default=0
val=2,1
val=3,2
val=4,3
val=5,4
val=6,5
val=7,6
-----
[3390]
inputtype=int
outputtype=struct
default=null
val=0,2826
val=1,2827
val=2,2828
val=3,2829
val=4,2830
val=5,2831
val=6,2832
val=7,2833
val=8,2834
val=9,2835
val=10,2836
val=11,2837
val=12,2838
val=13,2839
val=14,2840
val=15,2841
val=16,2842
val=17,2843
val=18,2844
val=19,2847
val=20,2848
val=21,2845
val=22,2846
val=23,2849
val=24,2850
-----
[2148]
inputtype=int
outputtype=namedobj
default=null
val=0,beginner_wand_6908
val=1,apprentice_wand_6910
val=2,teacher_wand_6912
val=3,master_wand_6914
val=4,infinity_hat_6918
val=5,infinity_top_6916
val=6,infinity_bottoms_6924
val=7,infinity_boots_6920
val=8,infinity_gloves_6922
val=9,mage's_book_6889
val=10,bones_to_peaches_6926
-----
[4184]
inputtype=namedobj
outputtype=int
default=0
val=sapphire_ring_1637,1
val=emerald_ring_1639,1
val=ruby_ring_1641,1
val=diamond_ring_1643,1
val=dragonstone_ring_1645,1
val=sapphire_necklace_1656,1
val=emerald_necklace_1658,1
val=ruby_necklace_1660,1
val=diamond_necklace_1662,1
val=dragon_necklace_1664,1
val=sapphire_amulet_(u)_1675,1
val=emerald_amulet_(u)_1677,1
val=ruby_amulet_(u)_1679,1
val=diamond_amulet_(u)_1681,1
val=dragonstone_amulet_(u)_1683,1
val=onyx_ring_6575,1
val=onyx_necklace_6577,1
val=onyx_amulet_(u)_6579,1
val=sapphire_bracelet_11072,1
val=emerald_bracelet_11076,1
val=ruby_bracelet_11085,1
val=diamond_bracelet_11092,1
val=dragonstone_bracelet_11115,1
val=onyx_bracelet_11130,1
val=slayer_ring_(8)_11866,1
val=zenyte_amulet_(u)_19501,1
val=zenyte_bracelet_19532,1
val=zenyte_necklace_19535,1
val=zenyte_ring_19538,1
val=opal_ring_21081,1
val=jade_ring_21084,1
val=topaz_ring_21087,1
val=opal_necklace_21090,1
val=jade_necklace_21093,1
val=topaz_necklace_21096,1
val=opal_amulet_(u)_21099,1
val=jade_amulet_(u)_21102,1
val=topaz_amulet_(u)_21105,1
val=opal_bracelet_21117,1
val=jade_bracelet_21120,1
val=topaz_bracelet_21123,1
val=slayer_ring_(eternal)_21268,1
-----
[2338]
inputtype=int
outputtype=string
default=
val=null,Rejoining
val=0,Unrestricted
val=1,60 secs delay
-----
[1735]
inputtype=int
outputtype=namedobj
default=null
val=0,helm_of_neitiznot_10828
val=1,spirit_shield_12829
val=2,book_of_war_12608
val=3,holy_book_3840
val=4,book_of_balance_3844
val=5,book_of_law_12610
val=6,unholy_book_3842
val=7,book_of_darkness_12612
val=8,archer_helm_3749
val=9,black_d'hide_body_2503
val=10,black_d'hide_chaps_2497
val=11,black_d'hide_vambraces_2491
val=12,warrior_helm_3753
val=13,berserker_helm_3751
val=14,rune_platebody_1127
val=15,rune_platelegs_1079
val=16,rune_plateskirt_1093
val=17,rune_kiteshield_1201
val=18,farseer_helm_3755
val=19,enchanted_top_7399
val=20,enchanted_robe_7398
val=21,climbing_boots_3105
val=22,rune_boots_4131
val=23,mystic_boots_(light)_4117
val=24,barrows_gloves_7462
val=25,rune_crossbow_9185
val=26,diamond_bolts_(e)_9243
val=27,magic_shortbow_(i)_12788
val=28,amethyst_arrow_21326
val=29,ancient_staff_4675
val=30,dragon_scimitar_4587
val=31,dragon_dagger_1215
val=32,granite_maul_4153
val=33,black_chinchompa_11959
val=34,amulet_of_strength_1725
val=35,amulet_of_glory_1704
val=36,rune_pouch_(l)_24416
val=37,blood_rune_565
val=38,death_rune_560
val=39,astral_rune_9075
val=40,earth_rune_557
val=41,water_rune_555
val=42,chaos_rune_562
val=43,soul_rune_566
val=44,fire_rune_554
val=45,air_rune_556
val=46,nature_rune_561
val=47,law_rune_563
val=48,cosmic_rune_564
val=49,lava_rune_4699
val=50,mud_rune_4698
val=51,mist_rune_4695
val=52,steam_rune_4694
val=53,dust_rune_4696
val=54,smoke_rune_4697
val=55,cooked_karambwan_3144
val=56,anglerfish_13441
val=57,saradomin_brew(4)_6685
val=58,sanfew_serum(4)_10925
val=59,super_restore(4)_3024
val=60,prayer_potion(4)_2434
val=61,super_combat_potion(4)_12695
val=62,ranging_potion(4)_2444
val=63,magic_potion(4)_3040
val=64,stamina_potion(4)_12625
val=65,anti-venom+(4)_12913
val=66,phoenix_necklace_11090
val=67,wilderness_champion_amulet_21433
val=68,dinh's_bulwark_21015
-----
[3212]
inputtype=int
outputtype=namedobj
default=null
val=0,black_wizard_hat_(g)_12453
val=1,black_wizard_robe_(g)_12449
val=2,black_skirt_(g)_12445
-----
[2751]
inputtype=namedobj
outputtype=int
default=1
val=impling_jar_11260,3
-----
[4597]
inputtype=int
outputtype=int
default=1
-----
[3787]
inputtype=int
outputtype=obj
default=air_rune_556
val=0,air_rune_556
val=1,water_rune_555
val=2,earth_rune_557
val=3,fire_rune_554
val=4,cosmic_rune_564
val=5,chaos_rune_562
val=6,nature_rune_561
val=7,law_rune_563
val=8,death_rune_560
val=9,astral_rune_9075
val=10,blood_rune_565
val=11,soul_rune_566
val=12,wrath_rune_21880
-----
[3791]
inputtype=int
outputtype=int
default=0
val=0,1000000
val=1,999848
val=2,999391
val=3,998630
val=4,997564
val=5,996195
val=6,994522
val=7,992546
val=8,990268
val=9,987688
val=10,984808
val=11,981627
val=12,978148
val=13,974370
val=14,970296
val=15,965926
val=16,961262
val=17,956305
val=18,951057
val=19,945519
val=20,939693
val=21,933580
val=22,927184
val=23,920505
val=24,913545
val=25,906308
val=26,898794
val=27,891007
val=28,882948
val=29,874620
val=30,866025
val=31,857167
val=32,848048
val=33,838671
val=34,829038
val=35,819152
val=36,809017
val=37,798636
val=38,788011
val=39,777146
val=40,766044
val=41,754710
val=42,743145
val=43,731354
val=44,719340
val=45,707107
val=46,694658
val=47,681998
val=48,669131
val=49,656059
val=50,642788
val=51,629320
val=52,615661
val=53,601815
val=54,587785
val=55,573576
val=56,559193
val=57,544639
val=58,529919
val=59,515038
val=60,500000
val=61,484810
val=62,469472
val=63,453990
val=64,438371
val=65,422618
val=66,406737
val=67,390731
val=68,374607
val=69,358368
val=70,342020
val=71,325568
val=72,309017
val=73,292372
val=74,275637
val=75,258819
val=76,241922
val=77,224951
val=78,207912
val=79,190809
val=80,173648
val=81,156434
val=82,139173
val=83,121869
val=84,104528
val=85,87156
val=86,69756
val=87,52336
val=88,34899
val=89,17452
val=90,0
val=91,-17452
val=92,-34899
val=93,-52336
val=94,-69756
val=95,-87156
val=96,-104528
val=97,-121869
val=98,-139173
val=99,-156434
val=100,-173648
val=101,-190809
val=102,-207912
val=103,-224951
val=104,-241922
val=105,-258819
val=106,-275637
val=107,-292372
val=108,-309017
val=109,-325568
val=110,-342020
val=111,-358368
val=112,-374607
val=113,-390731
val=114,-406737
val=115,-422618
val=116,-438371
val=117,-453990
val=118,-469472
val=119,-484810
val=120,-500000
val=121,-515038
val=122,-529919
val=123,-544639
val=124,-559193
val=125,-573576
val=126,-587785
val=127,-601815
val=128,-615661
val=129,-629320
val=130,-642788
val=131,-656059
val=132,-669131
val=133,-681998
val=134,-694658
val=135,-707107
val=136,-719340
val=137,-731354
val=138,-743145
val=139,-754710
val=140,-766044
val=141,-777146
val=142,-788011
val=143,-798636
val=144,-809017
val=145,-819152
val=146,-829038
val=147,-838671
val=148,-848048
val=149,-857167
val=150,-866025
val=151,-874620
val=152,-882948
val=153,-891007
val=154,-898794
val=155,-906308
val=156,-913545
val=157,-920505
val=158,-927184
val=159,-933580
val=160,-939693
val=161,-945519
val=162,-951057
val=163,-956305
val=164,-961262
val=165,-965926
val=166,-970296
val=167,-974370
val=168,-978148
val=169,-981627
val=170,-984808
val=171,-987688
val=172,-990268
val=173,-992546
val=174,-994522
val=175,-996195
val=176,-997564
val=177,-998630
val=178,-999391
val=179,-999848
val=180,-1000000
val=181,-999848
val=182,-999391
val=183,-998630
val=184,-997564
val=185,-996195
val=186,-994522
val=187,-992546
val=188,-990268
val=189,-987688
val=190,-984808
val=191,-981627
val=192,-978148
val=193,-974370
val=194,-970296
val=195,-965926
val=196,-961262
val=197,-956305
val=198,-951057
val=199,-945519
val=200,-939693
val=201,-933580
val=202,-927184
val=203,-920505
val=204,-913545
val=205,-906308
val=206,-898794
val=207,-891007
val=208,-882948
val=209,-874620
val=210,-866025
val=211,-857167
val=212,-848048
val=213,-838671
val=214,-829038
val=215,-819152
val=216,-809017
val=217,-798636
val=218,-788011
val=219,-777146
val=220,-766044
val=221,-754710
val=222,-743145
val=223,-731354
val=224,-719340
val=225,-707107
val=226,-694658
val=227,-681998
val=228,-669131
val=229,-656059
val=230,-642788
val=231,-629320
val=232,-615661
val=233,-601815
val=234,-587785
val=235,-573576
val=236,-559193
val=237,-544639
val=238,-529919
val=239,-515038
val=240,-500000
val=241,-484810
val=242,-469472
val=243,-453990
val=244,-438371
val=245,-422618
val=246,-406737
val=247,-390731
val=248,-374607
val=249,-358368
val=250,-342020
val=251,-325568
val=252,-309017
val=253,-292372
val=254,-275637
val=255,-258819
val=256,-241922
val=257,-224951
val=258,-207912
val=259,-190809
val=260,-173648
val=261,-156434
val=262,-139173
val=263,-121869
val=264,-104528
val=265,-87156
val=266,-69756
val=267,-52336
val=268,-34899
val=269,-17452
val=270,0
val=271,17452
val=272,34899
val=273,52336
val=274,69756
val=275,87156
val=276,104528
val=277,121869
val=278,139173
val=279,156434
val=280,173648
val=281,190809
val=282,207912
val=283,224951
val=284,241922
val=285,258819
val=286,275637
val=287,292372
val=288,309017
val=289,325568
val=290,342020
val=291,358368
val=292,374607
val=293,390731
val=294,406737
val=295,422618
val=296,438371
val=297,453990
val=298,469472
val=299,484810
val=300,500000
val=301,515038
val=302,529919
val=303,544639
val=304,559193
val=305,573576
val=306,587785
val=307,601815
val=308,615661
val=309,629320
val=310,642788
val=311,656059
val=312,669131
val=313,681998
val=314,694658
val=315,707107
val=316,719340
val=317,731354
val=318,743145
val=319,754710
val=320,766044
val=321,777146
val=322,788011
val=323,798636
val=324,809017
val=325,819152
val=326,829038
val=327,838671
val=328,848048
val=329,857167
val=330,866025
val=331,874620
val=332,882948
val=333,891007
val=334,898794
val=335,906308
val=336,913545
val=337,920505
val=338,927184
val=339,933580
val=340,939693
val=341,945519
val=342,951057
val=343,956305
val=344,961262
val=345,965926
val=346,970296
val=347,974370
val=348,978148
val=349,981627
val=350,984808
val=351,987688
val=352,990268
val=353,992546
val=354,994522
val=355,996195
val=356,997564
val=357,998630
val=358,999391
val=359,999848
-----
[1354]
inputtype=int
outputtype=namedobj
default=null
val=0,black-green_ankou_set_2749
val=1,black-purple_ankou_set_2750
val=2,black-white_ankou_set_2751
val=3,black-yellow_ankou_set_2752
val=4,gilded_ankou_set_2753
val=5,green-black_ankou_set_2754
val=6,purple-black_ankou_set_2755
val=7,red-black_ankou_set_2756
val=8,white-blue_ankou_set_2757
val=9,white-purple_ankou_set_2758
val=10,_2759
val=11,_2760
val=12,_2761
val=13,_2762
val=14,_2763
val=15,_2764
val=16,_2765
val=17,_2766
val=18,_2767
val=19,_2768
val=20,_2769
val=21,_2770
val=22,_2771
val=23,_2772
-----
[3196]
inputtype=int
outputtype=namedobj
default=null
val=0,sandwich_lady_hat_23312
val=1,sandwich_lady_top_23315
val=2,sandwich_lady_bottom_23318
-----
[496]
inputtype=obj
outputtype=int
default=null
val=null_20803,0
val=null_20804,1
val=null_20805,2
val=null_20806,3
val=null_20807,4
val=null_20808,5
val=null_20809,6
val=null_20810,7
val=null_20811,8
val=null_20812,9
val=null_20813,10
val=null_20814,11
val=null_20815,12
val=null_20816,13
val=null_20817,14
val=null_20818,15
val=null_20819,16
val=null_20820,17
val=null_20821,18
val=null_20822,19
val=null_20823,20
val=null_20824,21
val=null_20825,22
val=null_20826,23
val=null_20827,24
val=null_20828,25
val=null_20829,26
val=null_20830,27
val=null_20831,28
-----
[3184]
inputtype=int
outputtype=namedobj
default=null
val=0,ranging_cape_9756
val=1,ranging_hood_9758
-----
[83]
inputtype=int
outputtype=namedobj
default=null
val=0,rangers'_tunic_12596
val=1,rangers'_tights_23249
val=2,ranger_gloves_19994
-----
[3609]
inputtype=int
outputtype=null
default=null
val=0,2405
val=1,2346
val=2,2397
val=3,2404
val=4,2353
val=5,2344
val=6,2330
val=7,2331
-----
[3974]
inputtype=int
outputtype=string
default=
val=null,Completed
val=0,All
val=1,Incomplete
val=2,Complete
-----
[4403]
inputtype=int
outputtype=component
default=null
val=0,767:20
val=1,767:21
val=2,767:22
val=3,767:23
val=4,767:24
val=5,767:25
val=6,767:26
val=7,767:27
val=8,767:28
val=9,767:29
val=10,767:30
val=11,767:31
val=12,767:32
val=13,767:33
val=14,767:34
val=15,767:35
val=16,767:36
val=17,767:37
val=18,767:38
val=19,767:39
val=20,767:40
val=21,767:41
val=22,767:42
val=23,767:43
val=24,767:44
val=25,767:45
val=26,767:46
val=27,767:47
val=28,767:48
val=29,767:49
val=30,767:50
val=31,767:51
val=32,767:52
val=33,767:53
val=34,767:54
val=35,767:55
val=36,767:56
val=37,767:57
val=38,767:58
val=39,767:59
val=40,767:60
val=41,767:61
val=42,767:62
val=43,767:63
val=44,767:64
val=45,767:65
val=46,767:66
-----
[3216]
inputtype=int
outputtype=namedobj
default=null
val=0,green_d'hide_body_(g)_7370
val=1,green_d'hide_chaps_(g)_7378
-----
[2787]
inputtype=int
outputtype=namedobj
default=null
val=null,twisted_relic_hunter_(t3)_armour_set_24475
val=0,twisted_boots_(t3)_24393
val=1,twisted_hat_(t3)_24387
val=2,twisted_trousers_(t3)_24391
val=3,twisted_coat_(t3)_24389
-----
[1731]
inputtype=int
outputtype=coordgrid
default=null
val=1,3147_3849_0
val=2,3332_3815_0
val=3,2966_3811_0
val=4,3101_3529_0
val=5,3345_3578_0
-----
[2128]
inputtype=int
outputtype=namedobj
default=null
val=0,hill_giant_club_20756
-----
[3116]
inputtype=int
outputtype=namedobj
default=null
val=0,decorative_armour_11899
val=1,decorative_armour_11900
val=2,decorative_armour_11901
-----
[4006]
inputtype=int
outputtype=location
default=null
val=0,icicles_21134
val=1,stepping_stone_21120
val=2,null_21125
val=3,stepping_stone_21126
val=4,stepping_stone_21128
val=5,stepping_stone_21129
val=6,stepping_stone_21130
val=7,stepping_stone_21131
val=8,stepping_stone_21132
val=9,stepping_stone_21133
val=10,ice_21148
val=11,ice_21149
val=12,ice_21150
val=13,ice_21151
val=14,ice_21152
val=15,ice_21153
val=16,ice_21154
val=17,ice_21155
val=18,ice_21156
val=19,gate_21172
-----
[4633]
inputtype=int
outputtype=component
default=null
val=0,646:16
val=1,646:17
val=2,646:18
val=3,646:19
val=4,646:20
val=5,646:21
val=7,646:22
val=9,646:23
val=10,646:24
val=12,646:25
val=13,646:26
-----
[3545]
inputtype=namedobj
outputtype=int
default=null
val=slayer_helmet_(i)_25177,15
val=black_slayer_helmet_(i)_25179,16
val=green_slayer_helmet_(i)_25181,17
val=red_slayer_helmet_(i)_25183,18
val=purple_slayer_helmet_(i)_25185,19
val=turquoise_slayer_helmet_(i)_25187,20
val=hydra_slayer_helmet_(i)_25189,21
val=twisted_slayer_helmet_(i)_25191,22
val=granite_ring_(i)_25193,30
val=ring_of_suffering_(i)_25246,28
val=ring_of_suffering_(ri)_25248,29
val=salve_amulet(i)_25250,23
val=ring_of_the_gods_(i)_25252,27
val=tyrannical_ring_(i)_25254,25
val=treasonous_ring_(i)_25256,26
val=seers_ring_(i)_25258,0
val=archers_ring_(i)_25260,1
val=warrior_ring_(i)_25262,2
val=berserker_ring_(i)_25264,3
val=black_mask_(10)_(i)_25266,4
val=black_mask_(9)_(i)_25267,5
val=black_mask_(8)_(i)_25268,6
val=black_mask_(7)_(i)_25269,7
val=black_mask_(6)_(i)_25270,8
val=black_mask_(5)_(i)_25271,9
val=black_mask_(4)_(i)_25272,10
val=black_mask_(3)_(i)_25273,11
val=black_mask_(2)_(i)_25274,12
val=black_mask_(1)_(i)_25275,13
val=black_mask_(i)_25276,14
val=salve_amulet(ei)_25278,24
val=tztok_slayer_helmet_(i)_25902,31
val=vampyric_slayer_helmet_(i)_25908,32
val=tzkal_slayer_helmet_(i)_25914,33
-----
[2160]
inputtype=int
outputtype=namedobj
default=null
val=0,viggora's_chainmace_(u)_22542
val=1,craw's_bow_(u)_22547
val=2,thammaron's_sceptre_(u)_22552
val=3,amulet_of_avarice_22557
val=4,bracelet_of_ethereum_(uncharged)_21817
val=5,ancient_crystal_21804
val=6,ancient_relic_22305
val=7,ancient_effigy_22302
val=8,ancient_medallion_22299
val=9,ancient_statuette_21813
val=10,ancient_totem_21810
val=11,ancient_emblem_21807
val=12,revenant_cave_teleport_21802
val=13,revenant_ether_21820
-----
[4339]
inputtype=int
outputtype=location
default=null
val=0,null_14111
val=1,null_14110
val=2,null_14111
val=3,null_14110
val=4,null_14109
val=5,null_14110
val=6,null_14111
val=7,null_14110
val=8,null_14111
-----
[3807]
inputtype=int
outputtype=component
default=null
val=0,704:23
val=1,704:24
val=2,704:25
val=3,704:26
-----
[3910]
inputtype=int
outputtype=struct
default=3721
val=0,3722
val=1,3723
val=2,3723
val=3,3725
val=4,3721
val=5,3721
-----
[1564]
inputtype=int
outputtype=struct
default=null
val=0,3635
val=1,2777
val=2,2861
val=3,348
val=4,349
val=5,350
val=6,351
val=7,3731
val=8,4446
val=9,4447
val=10,4448
-----
[2064]
inputtype=int
outputtype=namedobj
default=null
val=0,commorb_6635
val=1,solus's_hat_6636
-----
[1961]
inputtype=enum
outputtype=int
default=0
val=1745,30
-----
[2557]
inputtype=int
outputtype=enum
default=null
val=0,2559
val=1,2561
-----
[2390]
inputtype=int
outputtype=namedobj
default=null
val=0,crystal_armour_seed_23956
val=1,crystal_weapon_seed_4207
val=2,enhanced_crystal_weapon_seed_25859
val=3,gauntlet_cape_23859
-----
[3871]
inputtype=int
outputtype=namedobj
default=null
val=0,raw_tuna_359
val=1,raw_swordfish_371
-----
[1302]
inputtype=int
outputtype=component
default=null
val=1,226:22
val=2,226:23
val=3,226:25
val=4,226:27
val=5,226:24
val=6,226:30
val=7,226:26
val=8,226:31
val=9,226:32
val=10,226:33
val=11,226:34
val=12,226:28
val=13,226:38
val=14,226:39
val=15,226:40
val=16,226:41
val=17,226:42
val=18,226:43
val=19,226:35
val=20,226:36
val=21,226:44
val=22,226:29
val=23,226:37
-----
[1135]
inputtype=enum
outputtype=int
default=0
val=1130,250
val=1131,215
val=1745,40
-----
[2981]
inputtype=namedobj
outputtype=int
default=1
val=body_rune_559,50
-----
[3148]
inputtype=int
outputtype=namedobj
default=null
val=0,ancestral_hat_21018
val=1,ancestral_robe_top_21021
val=2,ancestral_robe_bottom_21024
-----
[1076]
inputtype=int
outputtype=namedobj
default=null
val=null,saradomin_armour_set_(sk)_13042
val=0,saradomin_full_helm_2665
val=1,saradomin_platebody_2661
val=2,saradomin_plateskirt_3479
val=3,saradomin_kiteshield_2667
-----
[1505]
inputtype=int
outputtype=component
default=null
val=0,481:47
val=1,481:48
val=2,481:49
val=3,481:50
val=4,481:51
val=5,481:52
val=7,481:53
val=9,481:54
val=10,481:55
val=12,481:56
val=13,481:57
-----
[10500]
inputtype=int
outputtype=namedobj
default=null
val=0,chromium_ingot_28276
val=1,magus_icon_28291
val=2,eye_of_the_duke_28321
val=3,virtus_mask_26241
val=4,virtus_robe_top_26243
val=5,virtus_robe_bottom_26245
val=6,frozen_tablet_28333
val=7,awakener's_orb_28334
val=8,ice_quartz_28270
-----
[3013]
inputtype=namedobj
outputtype=int
default=1
val=leather_cowl_1167,1
-----
[1167]
inputtype=int
outputtype=int
default=null
val=1,50
val=2,150
val=3,500
-----
[3410]
inputtype=int
outputtype=string
default=
val=0,
val=1,a
val=2,an
val=3,a
val=4,a
val=5,an
val=6,a
-----
[2025]
inputtype=int
outputtype=namedobj
default=null
val=0,radimus_notes_714
val=1,radimus_notes_715
val=2,scrawled_note_717
val=3,a_scribbled_note_718
val=4,scrumpled_note_719
val=5,hollow_reed_727
val=6,hollow_reed_728
val=7,shaman's_tome_729
val=8,smashed_glass_733
val=9,yommi_tree_seeds_735
val=10,yommi_tree_seeds_736
val=11,bravery_potion_739
val=12,blue_hat_740
val=13,chunk_of_crystal_741
val=14,hunk_of_crystal_742
val=15,lump_of_crystal_743
val=16,heart_crystal_744
val=17,heart_crystal_745
val=18,holy_force_748
val=19,yommi_totem_749
val=20,gilded_totem_750
val=21,rock_968
val=22,sketch_720
-----
[1108]
inputtype=int
outputtype=namedobj
default=null
val=null,book_of_war_page_set_13155
val=0,bandos_page_1_12613
val=1,bandos_page_2_12614
val=2,bandos_page_3_12615
val=3,bandos_page_4_12616
-----
[770]
inputtype=stat
outputtype=int
default=null
val=0,154
val=1,158
val=2,155
val=3,159
val=4,156
val=5,160
val=6,157
val=7,169
val=8,171
val=9,172
val=10,168
val=11,170
val=12,164
val=13,167
val=14,166
val=15,162
val=16,161
val=17,163
val=18,173
val=19,174
val=20,165
val=21,176
val=22,175
-----
[3839]
inputtype=int
outputtype=string
default=Chat-channel
val=1,Your Clan
val=2,View another clan
val=3,Grouping
val=4,Iron Group
val=5,Hardcore Iron Group
val=6,Unranked Iron Group
-----
[2584]
inputtype=namedobj
outputtype=int
default=1
val=water_rune_555,75
val=mind_rune_558,50
val=staff_of_air_1381,1
-----
[1993]
inputtype=int
outputtype=namedobj
default=null
val=0,enchanted_beef_522
val=1,enchanted_rat_523
val=2,enchanted_bear_524
val=3,enchanted_chicken_525
-----
[2525]
inputtype=int
outputtype=namedobj
default=null
val=0,coins_995
-----
[2616]
inputtype=int
outputtype=obj
default=null
val=0,adamant_sword_1287
-----
[2954]
inputtype=int
outputtype=namedobj
default=null
val=0,coins_995
-----
[4033]
inputtype=int
outputtype=string
default=Nearest-Neighbour
val=0,Nearest-Neighbour
val=1,Linear
val=2,Bicubic
-----
[679]
inputtype=obj
outputtype=boolean
default=0
val=armadyl_crossbow_11785,1
val=staff_of_the_dead_11791,1
val=armadyl_godsword_11802,1
val=bandos_godsword_11804,1
val=saradomin_godsword_11806,1
val=zamorak_godsword_11808,1
val=armadyl_helmet_11826,1
val=armadyl_chestplate_11828,1
val=armadyl_chainskirt_11830,1
val=bandos_chestplate_11832,1
val=bandos_tassets_11834,1
val=bandos_boots_11836,1
val=dragon_claws_13652,1
val=elder_maul_21003,1
val=kodai_wand_21006,1
val=dragon_hunter_crossbow_21012,1
-----
[3942]
inputtype=int
outputtype=struct
default=null
val=0,323
-----
[250]
inputtype=int
outputtype=obj
default=parlour_8395
val=1,parlour_8395
val=2,garden_8415
val=3,kitchen_8396
val=4,dining_room_8397
val=5,bedroom_8398
val=6,games_room_8399
val=7,hall_8401
val=8,hall_8402
val=9,hall_8403
val=10,hall_8404
val=11,chapel_8405
val=12,workshop_8406
val=13,study_8407
val=14,portal_chamber_8408
val=15,throne_room_8409
val=16,oubliette_8410
val=17,dungeon_corridor_8411
val=18,dungeon_cross_8412
val=19,dungeon_stairs_8413
val=20,treasure_room_8414
val=21,formal_garden_8416
val=22,combat_room_8400
val=23,costume_room_9842
val=24,menagerie_12725
val=25,menagerie_12726
val=26,superior_garden_20653
val=27,achievement_gallery_20654
val=28,portal_nexus_22704
val=29,league_hall_25366
-----
[10016]
inputtype=int
outputtype=string
default=
val=0,Easy - 80x XP, No drop rate bonus, 1x Exiles Points
val=1,Medium - 40x XP, Slightly better drop rate, 1.3x Exiles Points
val=2,Hard - 20x XP, Better drop rate, 1.6x Exiles Points
val=3,Extreme - 5x XP, Significantly better drop rate, 2x Exiles Points
-----
[2096]
inputtype=int
outputtype=string
default=No Location
val=0,Al Kharid
val=1,Arceuus
val=2,Ardougne
val=3,Brimhaven
val=4,Burgh de Rott
val=5,Burthorpe
val=6,Canifis
val=7,Kharidian Desert
val=8,Dorgesh-Kaan
val=9,Draynor Village
val=10,Entrana
val=11,Falador
val=12,Feldip Hills
val=13,Gnome Stronghold
val=14,Goblin Village
val=15,Hosidius
val=16,Isafdar
val=17,Kebos Lowlands
val=18,Keldagrim
val=19,Kingstown
val=20,Lletya
val=21,Lovakengj
val=22,Lumbridge
val=23,Lunar Isle
val=24,Meiyerditch
val=25,Miscellania
val=26,Mort'ton
val=27,Mos Le'Harmless
val=28,Nardah
val=29,Port Phasmatys
val=30,Port Piscarilius
val=31,Piscatoris
val=32,Port Sarim
val=33,Rellekka
val=34,Rimmington
val=35,Seers' Village
val=36,Shayzien
val=37,Shilo Village
val=38,Silvarea
val=39,Slepe
val=40,Sophanem
val=41,Tai Bwo Wannai
val=42,Taverley
val=43,Tree Gnome Village
val=44,Troll Stronghold
val=45,Varrock
val=46,Wilderness
val=47,Witchaven
val=48,Yanille
-----
[4065]
inputtype=int
outputtype=string
default=On when carrying a pickaxe
val=0,On
val=1,On when carrying a pickaxe
val=2,Off
-----
[3636]
inputtype=int
outputtype=string
default=null
val=0,trailblazer_teleport_draw_line
val=1,trailblazer_teleport_draw_symbol
val=2,trailblazer_teleport_flyaway
val=3,trailblazer_teleport_spin
val=4,trailblazer_unlock_map
val=5,trailblazer_unlock_power
val=6,trailblazer_unlock_raise
val=7,trailblazer_unlock_twunkles
-----
[4081]
inputtype=location
outputtype=int
default=0
val=rocks_17042,1
val=rocks_17043,1
val=wall_17047,1
val=broken_raft_17068,1
val=strong_tree_17074,1
val=null_26561,1
val=null_26562,1
val=null_31849,1
val=null_31852,1
val=null_39541,1
val=null_39542,1
val=null_40960,1
val=null_40962,1
-----
[850]
inputtype=int
outputtype=int
default=null
val=1,306
val=2,358
val=3,491
val=4,334
val=5,394
val=6,null
val=7,370
val=8,null
val=9,null
val=10,null
val=11,null
val=12,null
val=13,344
val=14,330
val=15,377
val=16,377
val=17,null
val=18,null
val=19,320
val=20,376
val=21,null
val=22,338
val=23,304
val=24,362
val=25,323
-----
[3093]
inputtype=int
outputtype=namedobj
default=null
val=0,graceful_hood_13627
val=1,graceful_top_13631
val=2,graceful_legs_13633
val=3,graceful_cape_13629
val=4,graceful_gloves_13635
val=5,graceful_boots_13637
-----
[1409]
inputtype=int
outputtype=model
default=null
val=0,20522
val=1,20522
val=2,20523
val=3,20524
val=4,20525
val=5,20525
-----
[1790]
inputtype=int
outputtype=string
default=null
val=1,a person with a fish tail
val=2,a pufferfish
val=3,a snake in the shape of a hoop
val=4,a dragon egg
val=5,a crab shell
val=6,it could be used for fuel
val=7,a 'big' foot print
val=8,a zygomite
val=9,a clam shell
val=10,a foot print of a mythical animal
val=11,an imprint of a posterior
val=12,an ancient cave painting
val=13,a cyclopse with two heads
val=14,a dragon skull
val=15,an unusual lobster
val=16,a tar creature
val=17,a merfish with legs
val=18,a toucan with two heads
val=19,a bird with a snail shell on its back
val=20,a tortoise with a mushroom on its shell
val=21,a cross between a dark beast and a shark
val=22,a cross between a mushroom and kangaroo
val=23,a squirrel with huge teeth
val=24,a creature with a giant skull and small body
val=25,a giant dragonfly
val=26,a lava creature
val=27,a great leviathan
val=28,a great leviathan
val=29,a great leviathan
val=30,a great leviathan
val=31,a great leviathan
val=32,a tree sized mushroom
val=33,a huge pitcher plant
val=34,scary mushrooms
val=35,a plant with a stack of mushrooms
val=36,a big mushroom
-----
[4097]
inputtype=int
outputtype=namedobj
default=null
val=0,ancient_hilt_26370
val=1,nihil_horn_26372
val=2,zaryte_vambraces_26235
val=3,torva_full_helm_(damaged)_26376
val=4,torva_platebody_(damaged)_26378
val=5,torva_platelegs_(damaged)_26380
val=6,nihil_shard_26231
-----
[4049]
inputtype=int
outputtype=namedobj
default=null
val=0,ancient_ceremonial_top_26221
val=1,ancient_ceremonial_legs_26223
val=2,ancient_ceremonial_mask_26225
val=3,ancient_ceremonial_gloves_26227
val=4,ancient_ceremonial_boots_26229
-----
[1870]
inputtype=obj
outputtype=int
default=null
val=_20307,0
val=_20308,1
val=_20309,2
val=_20310,3
val=_20311,4
val=_20312,5
val=_20313,6
val=_20314,7
val=_20315,8
val=_20316,9
val=_20317,10
val=_20318,11
val=_20319,12
val=_20320,13
val=_20321,14
val=_20322,15
val=_20323,16
val=_20324,17
val=_20325,18
val=_20326,19
val=_20327,20
val=_20328,21
val=_20329,22
val=_20330,23
-----
[4145]
inputtype=int
outputtype=enum
default=null
val=0,4143
val=1,4144
-----
[818]
inputtype=int
outputtype=int
default=0
val=0,1
val=1,2
val=2,3
val=3,4
val=4,5
val=5,6
val=6,7
val=7,8
val=8,9
val=9,10
val=10,11
val=11,12
val=12,13
val=13,14
val=14,15
val=15,16
val=16,17
val=17,18
val=18,19
val=19,20
val=20,21
val=21,22
val=22,23
val=23,24
val=24,25
val=25,26
val=26,27
val=27,28
val=28,29
val=29,30
val=30,31
val=31,32
val=32,33
val=33,34
val=34,35
val=35,36
val=36,37
val=37,38
val=38,39
val=39,40
val=40,41
val=41,42
val=42,43
val=43,44
val=44,45
val=45,46
val=46,47
val=47,48
val=48,49
val=49,50
val=50,51
val=51,52
val=52,53
val=53,54
val=54,55
val=55,56
val=56,57
val=57,58
val=58,59
val=59,60
val=60,61
val=61,62
val=62,63
val=63,64
val=64,65
val=65,66
val=66,67
val=67,68
val=68,69
val=69,70
val=70,71
val=71,72
val=72,73
val=73,74
val=74,75
val=75,76
val=76,77
val=77,78
val=78,79
val=79,80
val=80,81
val=81,82
val=82,83
val=83,84
val=84,85
val=85,86
val=86,87
val=87,88
val=88,89
val=89,90
val=90,91
val=91,92
val=92,93
val=93,94
val=94,95
val=95,96
val=96,97
val=97,98
val=98,99
val=99,100
val=100,101
val=101,102
val=102,103
val=103,104
val=104,105
val=105,106
val=106,107
val=107,108
val=108,109
val=109,110
val=110,111
val=111,112
val=112,113
val=113,114
val=114,115
val=115,116
val=116,117
val=117,118
val=118,119
val=119,120
val=120,121
val=121,122
val=122,123
val=123,124
val=124,125
val=125,126
val=126,127
val=127,128
val=128,129
val=129,130
val=130,131
val=131,132
val=132,133
val=133,134
val=134,135
val=135,136
val=136,137
val=137,138
val=138,139
val=139,140
val=140,141
val=141,142
val=142,143
val=143,144
val=144,145
val=145,146
val=146,147
val=147,148
val=148,149
val=149,150
val=150,151
val=151,152
val=152,153
val=153,154
val=154,155
val=155,156
val=156,157
val=157,158
val=158,159
val=159,160
val=160,161
val=161,162
val=162,163
val=163,164
val=164,165
val=165,166
val=166,167
val=167,168
val=168,169
val=169,170
val=170,171
val=171,172
val=172,173
val=173,174
val=174,175
val=175,176
val=176,177
val=177,178
val=178,179
val=179,180
val=180,181
val=181,182
val=182,183
val=183,184
val=184,185
val=185,186
val=186,187
val=187,188
val=188,189
val=189,190
val=190,191
val=191,192
val=192,193
val=193,194
val=194,195
val=195,196
val=196,197
val=197,198
val=198,199
val=199,200
val=200,201
val=201,202
val=202,203
val=203,204
val=204,205
val=205,206
val=206,207
val=207,208
val=208,209
val=209,210
val=210,211
val=211,212
val=212,213
val=213,214
val=214,215
val=215,216
val=216,217
val=217,218
val=218,219
val=219,220
val=220,221
val=221,222
val=222,223
val=223,224
val=224,225
val=225,226
val=226,227
val=227,228
val=228,229
val=229,230
val=230,231
val=231,232
val=232,233
val=233,234
val=234,235
val=235,236
val=236,237
val=237,238
val=238,239
val=239,240
val=240,241
val=241,242
val=242,243
val=243,244
val=244,245
val=245,246
val=246,247
val=247,248
val=248,249
val=249,250
val=250,251
val=251,252
val=252,253
val=253,254
val=254,255
val=255,256
val=256,257
val=257,258
val=258,259
val=259,260
val=260,261
val=261,262
val=262,263
val=263,264
val=264,265
val=265,266
val=266,267
val=267,268
val=268,269
val=269,270
val=270,271
val=271,272
val=272,273
val=273,274
val=274,275
val=275,276
val=276,277
val=277,278
val=278,279
val=279,280
val=280,281
val=281,282
val=282,283
val=283,284
val=284,285
val=285,286
val=286,287
val=287,288
val=288,289
val=289,290
val=290,291
val=291,292
val=292,293
val=293,294
val=294,295
val=295,296
val=296,297
val=297,298
val=298,299
val=299,300
val=300,301
val=301,302
val=302,303
val=303,304
val=304,305
val=305,306
val=306,307
val=307,308
val=308,309
val=309,310
val=310,311
val=311,312
val=312,313
val=313,314
val=314,315
val=315,316
val=316,317
val=317,318
val=318,319
val=319,320
val=320,321
val=321,322
val=322,323
val=323,324
val=324,325
val=325,326
val=326,327
val=327,328
val=328,329
val=329,330
val=330,331
val=331,332
val=332,333
val=333,334
val=334,335
val=335,336
val=336,337
val=337,338
val=338,339
val=339,340
val=340,341
val=341,342
val=342,343
val=343,344
val=344,345
val=345,346
val=346,347
val=347,348
val=348,349
val=349,350
val=350,351
val=351,352
val=352,353
val=353,354
val=354,355
val=355,356
val=356,357
val=357,358
val=358,359
val=359,360
val=360,361
val=361,362
val=362,363
val=363,364
val=364,365
val=365,366
val=366,367
val=367,368
val=368,369
val=369,370
val=370,371
val=371,372
val=372,373
val=373,374
val=374,375
val=375,376
val=376,377
val=377,378
val=378,379
val=379,380
val=380,381
val=381,382
val=382,383
val=383,384
val=384,385
val=385,386
val=386,387
val=387,388
val=388,389
val=389,390
val=390,391
val=391,392
val=392,393
val=393,394
val=394,395
val=395,396
val=396,397
val=397,398
val=398,399
val=399,400
val=400,401
val=401,402
val=402,403
val=403,404
val=404,405
val=405,406
val=406,407
val=407,408
val=408,409
val=409,410
val=410,411
val=411,412
val=412,413
val=413,414
val=414,415
val=415,416
val=416,417
val=417,418
val=418,419
val=419,420
val=420,421
val=421,422
val=422,423
val=423,424
val=424,425
val=425,426
val=426,427
val=427,428
val=428,429
val=429,430
val=430,431
val=431,432
val=432,433
val=433,434
val=434,435
val=435,436
val=436,437
val=437,438
val=438,439
val=439,440
val=440,441
val=441,442
val=442,443
val=443,444
val=444,445
val=445,446
val=446,447
val=447,448
val=448,449
val=449,450
val=450,451
val=451,452
val=452,453
val=453,454
val=454,455
val=455,456
val=456,457
val=457,458
val=458,459
val=459,460
val=460,461
val=461,462
val=462,463
val=463,464
val=464,465
val=465,466
val=466,467
val=467,468
val=468,469
val=469,470
val=470,471
val=471,472
val=472,473
val=473,474
val=474,475
val=475,476
val=476,477
val=477,478
val=478,479
val=479,480
val=480,481
val=481,482
val=482,483
val=483,484
val=484,485
val=485,486
val=486,487
val=487,488
val=488,489
val=489,490
val=490,491
val=491,492
val=492,493
val=493,494
val=494,495
val=495,496
val=496,497
val=497,498
val=498,499
val=499,500
val=500,501
val=501,502
val=502,503
val=503,504
val=504,505
val=505,506
val=506,507
val=507,508
val=508,513
val=509,511
val=510,509
val=511,510
val=512,512
val=513,514
val=514,515
val=515,516
val=516,517
val=517,518
val=518,519
val=519,520
val=520,521
val=521,522
val=522,523
val=523,524
val=524,525
val=525,526
val=526,527
val=527,528
val=528,529
val=529,530
val=530,531
val=531,532
val=532,533
val=533,534
val=534,535
val=535,536
val=536,537
val=537,538
val=538,539
val=539,540
val=540,541
val=541,542
val=542,543
val=543,544
val=544,545
val=545,546
val=546,547
val=547,548
val=548,549
val=549,550
val=550,551
val=551,552
val=552,553
val=553,554
val=554,555
val=555,556
val=556,557
val=557,558
val=558,559
val=559,560
val=560,561
val=561,562
val=562,563
val=563,564
val=564,565
val=565,566
val=566,567
val=567,568
val=568,569
val=569,570
val=570,571
val=571,572
val=572,573
val=573,574
val=574,575
val=575,576
val=576,577
val=577,578
val=578,579
val=579,580
val=580,581
val=581,582
val=582,583
val=583,584
val=584,585
val=585,586
val=586,587
val=587,588
val=588,589
val=589,590
val=590,591
val=591,592
val=592,593
val=593,594
val=594,595
val=595,596
val=596,597
val=597,598
val=598,599
val=599,600
val=600,601
val=601,602
val=602,603
val=603,604
val=604,605
val=605,606
val=606,607
val=607,608
val=608,609
val=609,610
val=610,611
val=611,612
val=612,613
val=613,614
val=614,615
val=615,616
val=616,617
val=617,618
val=618,619
val=619,620
val=620,621
val=621,622
val=622,623
val=623,624
val=624,625
val=625,626
val=626,627
val=627,628
val=628,629
val=629,630
val=630,631
val=631,632
val=632,633
val=633,634
val=634,635
val=635,636
val=636,637
val=637,638
val=638,639
val=639,640
val=640,641
val=641,642
val=642,643
val=643,644
val=644,645
val=645,646
val=646,647
val=647,648
val=648,649
val=649,650
val=650,651
val=651,652
val=652,653
val=653,654
val=654,655
val=655,656
val=656,657
val=657,658
val=658,659
val=659,660
val=660,661
val=661,662
val=662,663
val=663,664
val=664,665
val=665,666
val=666,667
val=667,668
val=668,669
val=669,670
val=670,671
val=671,672
val=672,673
val=673,674
val=674,675
val=675,676
val=676,677
val=677,678
val=678,679
val=679,680
val=680,681
val=681,682
val=682,683
val=683,684
val=684,685
val=685,686
val=686,687
val=687,688
val=688,689
val=689,690
val=690,691
val=691,692
val=692,693
val=693,694
val=694,695
val=695,696
val=696,697
val=697,698
val=698,699
val=699,700
val=700,701
val=701,702
val=702,703
val=703,704
val=704,705
val=705,706
val=706,715
val=707,716
val=708,717
val=709,718
val=710,719
val=711,720
val=712,721
val=713,722
val=714,723
val=715,724
val=716,725
val=717,726
val=718,727
val=719,728
val=720,729
val=721,730
val=722,731
val=723,732
val=724,733
val=725,734
val=726,735
val=727,736
val=728,737
val=729,738
val=730,739
-----
[802]
inputtype=int
outputtype=string
default=?
val=1,Barrelchest
val=2,Giant scarab
val=3,Dessous
val=4,Kamil
val=5,Damis
val=6,Fareed
val=7,Elvarg
val=8,The Inadequacy
val=9,The Everlasting
val=10,The Untouchable
val=11,Tanglefoot
val=12,Chronozon
val=13,Bouncer
val=14,Ice Troll King
val=15,Black Demon
val=16,Glod
val=17,Treus Dayth
val=18,Black Knight Titan
val=19,Dagannoth Mother
val=20,Evil Chicken
val=21,Culinaromancer
val=22,Agrith-Na-Na
val=23,Flambeed
val=24,Karamel
val=25,Dessourt
val=26,Gelatinnoth Mother
val=27,Nezikchened
val=28,Tree spirit
val=29,Me
val=30,Jungle Demon
val=31,The Kendal
val=32,Giant Roc
val=33,Slagilith
val=34,Moss Guardian
val=35,Skeleton Hellhound
val=36,Agrith Naar
val=37,King Roald
val=38,Khazard Warlord
val=39,Dad
val=40,Arrg
val=41,Count Draynor
val=42,Witch's Experiment
val=43,Nazastarool
val=44,Sand Snake
val=45,Corsair Traitor
val=46,Corrupt Lizardman
val=47,Trapped Soul
val=48,Elven Traitor
val=49,Essyllt
val=50,Headless Beast
val=61,Customisable Rumble (normal)
val=123,Customisable Rumble (hard)
val=124,Endurance (normal)
val=125,Endurance (hard)
val=126,Rumble (normal)
val=127,Rumble (hard)
-----
[373]
inputtype=enum
outputtype=enum
default=null
val=374,375
val=913,914
val=915,916
val=917,918
val=919,920
val=921,922
val=923,924
val=925,926
val=927,928
val=929,930
val=931,932
val=2336,2337
val=2338,2339
val=2340,2341
-----
[3077]
inputtype=obj
outputtype=enum
default=null
val=shade_robe_top_546,3167
val=black_platebody_(t)_2583,3201
val=black_platebody_(g)_2591,3202
val=adamant_platebody_(t)_2599,3218
val=adamant_platebody_(g)_2607,3219
val=rune_platebody_(g)_2615,3233
val=rune_platebody_(t)_2623,3234
val=zamorak_platebody_2653,3235
val=saradomin_platebody_2661,3236
val=guthix_platebody_2669,3237
val=mime_mask_3057,3162
val=splitbark_helm_3385,3086
val=gilded_platebody_3481,3238
val=decorative_helm_4071,3112
val=mystic_hat_4089,3078
val=mystic_hat_(dark)_4099,3079
val=mystic_hat_(light)_4109,3080
val=ham_shirt_4298,3136
val=decorative_helm_4506,3113
val=decorative_helm_4511,3114
val=rogue_mask_5554,3119
val=initiate_hauberk_5575,3123
val=mourner_top_6065,3125
val=ghostly_boots_6106,3087
val=rock-shell_helm_6128,3121
val=spined_helm_6131,3120
val=skeletal_helm_6137,3082
val=lederhosen_top_6180,3166
val=frog_mask_6188,3163
val=snakeskin_bandana_6326,3161
val=villager_hat_6345,27
val=villager_hat_6355,28
val=villager_hat_6365,29
val=villager_hat_6375,30
val=white_platebody_6617,3122
val=camo_top_6654,3165
val=mudskipper_hat_6665,3110
val=bobble_hat_6856,3271
val=bobble_scarf_6857,3272
val=red_marionette_6867,3270
val=infinity_top_6916,3083
val=studded_body_(g)_7362,3197
val=studded_body_(t)_7364,3198
val=green_d'hide_body_(g)_7370,3216
val=green_d'hide_body_(t)_7372,3217
val=blue_d'hide_body_(g)_7374,3230
val=blue_d'hide_body_(t)_7376,3231
val=blue_wizard_robe_(g)_7390,3199
val=blue_wizard_robe_(t)_7392,3200
val=enchanted_top_7399,3232
val=zombie_shirt_7592,3164
val=void_knight_top_8839,3117
val=blue_tricorn_hat_8959,3100
val=green_tricorn_hat_8960,3101
val=red_tricorn_hat_8961,3102
val=brown_tricorn_hat_8962,3103
val=black_tricorn_hat_8963,3104
val=purple_tricorn_hat_8964,3105
val=grey_tricorn_hat_8965,3106
val=moonclan_hat_9069,3088
val=lunar_helm_9096,3089
val=proselyte_hauberk_9674,3124
val=attack_cape_9747,3168
val=strength_cape_9750,3170
val=defence_cape_9753,3169
val=ranging_cape_9756,3184
val=prayer_cape_9759,3183
val=magic_cape_9762,3181
val=runecraft_cape_9765,3185
val=hitpoints_cape_9768,3171
val=agility_cape_9771,3172
val=herblore_cape_9774,3180
val=thieving_cape_9777,3188
val=crafting_cape_9780,3175
val=fletching_cape_9783,3179
val=slayer_cape_9786,3186
val=construct._cape_9789,3174
val=mining_cape_9792,3182
val=smithing_cape_9795,3187
val=fishing_cape_9798,3178
val=cooking_cape_9801,3173
val=firemaking_cape_9804,3177
val=woodcutting_cape_9807,3189
val=farming_cape_9810,3176
val=quest_point_cape_9813,3191
val=skeleton_mask_9925,3273
val=bomber_jacket_9944,3135
val=hunter_cape_9948,3190
val=kyatt_top_10037,3128
val=larupia_top_10043,3127
val=graahk_top_10049,3126
val=wood_camo_top_10053,3131
val=jungle_camo_top_10057,3130
val=desert_camo_top_10061,3132
val=polar_camo_top_10065,3129
val=zamorak_d'hide_body_10370,3244
val=guthix_d'hide_body_10378,3243
val=saradomin_d'hide_body_10386,3242
val=black_elegant_shirt_10400,3220
val=red_elegant_shirt_10404,3203
val=blue_elegant_shirt_10408,3205
val=green_elegant_shirt_10412,3204
val=purple_elegant_shirt_10416,3221
val=saradomin_robe_top_10458,3239
val=zamorak_robe_top_10460,3241
val=guthix_robe_top_10462,3240
val=hard_hat_10862,3133
val=lumberjack_top_10939,3134
val=chicken_head_11021,3274
val=graceful_hood_11850,3091
val=decorative_armour_11896,3115
val=decorative_armour_11899,3116
val=cow_mask_11919,3275
val=prospector_helmet_12013,3137
val=ancient_robe_top_12193,3227
val=bronze_platebody_(g)_12205,3207
val=bronze_platebody_(t)_12215,3206
val=iron_platebody_(t)_12225,3208
val=iron_platebody_(g)_12235,3209
val=armadyl_robe_top_12253,3226
val=bandos_robe_top_12265,3228
val=mithril_platebody_(g)_12277,3224
val=mithril_platebody_(t)_12287,3225
val=pink_elegant_shirt_12315,3222
val=crier_hat_12319,3229
val=red_d'hide_body_(g)_12327,3245
val=red_d'hide_body_(t)_12331,3246
val=gold_elegant_shirt_12347,3223
val=black_d'hide_body_(g)_12381,3258
val=black_d'hide_body_(t)_12385,3259
val=royal_gown_top_12393,3257
val=dragon_full_helm_(g)_12417,3263
val=light_infinity_top_12420,3084
val=musketeer_tabard_12441,3260
val=black_wizard_hat_(g)_12453,3212
val=black_wizard_hat_(t)_12455,3213
val=dark_infinity_top_12458,3085
val=ancient_platebody_12460,3249
val=armadyl_platebody_12470,3247
val=bandos_platebody_12480,3248
val=ancient_d'hide_body_12492,3252
val=bandos_d'hide_body_12500,3251
val=armadyl_d'hide_body_12508,3250
val=rangers'_tunic_12596,83
val=santa_mask_12887,3276
val=antisanta_mask_12892,3277
val=elite_void_top_13072,3118
val=tiger_toy_13215,3278
val=music_cape_13221,3193
val=angler_hat_13258,3138
val=max_cape_13280,3194
val=gravedigger_mask_13283,3279
val=deadman's_chest_13317,3155
val=max_cape_13342,3194
val=shayzien_helm_(1)_13359,3139
val=shayzien_helm_(2)_13364,3140
val=shayzien_helm_(3)_13369,3141
val=shayzien_helm_(4)_13374,3142
val=shayzien_helm_(5)_