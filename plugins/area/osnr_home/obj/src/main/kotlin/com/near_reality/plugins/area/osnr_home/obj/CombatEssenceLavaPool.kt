package com.near_reality.plugins.area.osnr_home.obj

import com.near_reality.game.item.CustomObjectId
import com.zenyte.game.GameInterface
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.`object`.ObjectAction
import com.zenyte.game.world.`object`.WorldObject

class CombatEssenceLavaPool : ObjectAction {
    override fun handleObjectAction(
        player: Player?,
        `object`: WorldObject?,
        name: String?,
        optionId: Int,
        option: String?
    ) {
        if(option == "Sacrifice Items") {
            GameInterface.COMBAT_ESSENCE_EXCHANGE.open(player)
        }
        if(option == "Item Sacrifice Values") {
            GameInterface.COMBAT_ESSENCE_PRICES.open(player)
        }
    }

    override fun getObjects(): Array<Int> {
        return arrayOf(CustomObjectId.COMBAT_ESSENCE_LAVA_POOL)
    }
}